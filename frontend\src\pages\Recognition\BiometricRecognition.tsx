import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  Space,
  Row,
  Col,
  Upload,
  Image,
  List,
  Tag,
  Progress,
  Alert,
  Tabs,
  Select,
  Switch,
  Slider,
  message,
  Modal,
  Divider,
  Statistic,
  Badge,
  Steps,
  Result,
  Spin,
} from 'antd';
import {
  ArrowLeftOutlined,
  UserOutlined,
  UploadOutlined,
  CameraOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ZoomInOutlined,
  ClearOutlined,
  ScanOutlined,
  EyeOutlined,
  SafetyCertificateOutlined,
  ScanOutlined as FingerprintOutlined,
  AudioOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

interface BiometricResult {
  id: string;
  type: 'face' | 'fingerprint' | 'iris' | 'voice' | 'palm';
  confidence: number;
  features: Record<string, any>;
  timestamp: string;
  status: 'success' | 'failed' | 'partial';
  matchScore?: number;
  liveness?: boolean;
}

interface BiometricAnalysis {
  id: string;
  filename?: string;
  url?: string;
  size?: number;
  type: 'face' | 'fingerprint' | 'iris' | 'voice' | 'palm';
  results: BiometricResult[];
  timestamp: string;
  processingTime: number;
  overallScore: number;
}

const BiometricRecognition: React.FC = () => {
  const navigate = useNavigate();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [analyses, setAnalyses] = useState<BiometricAnalysis[]>([]);
  const [selectedAnalysis, setSelectedAnalysis] = useState<BiometricAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [selectedType, setSelectedType] = useState<string>('face');
  const [confidence, setConfidence] = useState(80);
  const [enableLiveness, setEnableLiveness] = useState(true);
  const [enableAntiSpoofing, setEnableAntiSpoofing] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCapturing, setIsCapturing] = useState(false);

  const cameraRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  // 生物识别类型配置
  const biometricTypes = [
    {
      value: 'face',
      label: '人脸识别',
      icon: <UserOutlined />,
      color: '#1890ff',
      description: '基于面部特征的身份认证',
      features: ['活体检测', '防伪检测', '多角度识别', '表情无关']
    },
    {
      value: 'fingerprint',
      label: '指纹识别',
      icon: <FingerprintOutlined />,
      color: '#52c41a',
      description: '基于指纹纹路的身份认证',
      features: ['纹路分析', '特征点提取', '防伪检测', '多指识别']
    },
    {
      value: 'iris',
      label: '虹膜识别',
      icon: <EyeOutlined />,
      color: '#722ed1',
      description: '基于虹膜纹理的身份认证',
      features: ['高精度识别', '活体检测', '远距离识别', '稳定性强']
    },
    {
      value: 'voice',
      label: '声纹识别',
      icon: <AudioOutlined />,
      color: '#fa8c16',
      description: '基于声音特征的身份认证',
      features: ['语音分析', '情感无关', '噪声过滤', '多语言支持']
    },
    {
      value: 'palm',
      label: '掌纹识别',
      icon: <SafetyCertificateOutlined />,
      color: '#eb2f96',
      description: '基于掌纹特征的身份认证',
      features: ['掌纹分析', '静脉识别', '非接触式', '高安全性']
    },
  ];

  // 模拟生物识别结果
  const mockResults: Record<string, BiometricResult[]> = {
    face: [
      {
        id: '1',
        type: 'face',
        confidence: 0.95,
        features: {
          age: '25-35',
          gender: '女性',
          emotion: '中性',
          glasses: false,
          mask: false,
          pose: '正面',
        },
        timestamp: new Date().toLocaleString(),
        status: 'success',
        matchScore: 0.92,
        liveness: true,
      }
    ],
    fingerprint: [
      {
        id: '2',
        type: 'fingerprint',
        confidence: 0.88,
        features: {
          pattern: '弓形纹',
          quality: '优秀',
          minutiae: 45,
          ridgeCount: 128,
        },
        timestamp: new Date().toLocaleString(),
        status: 'success',
        matchScore: 0.85,
      }
    ],
    iris: [
      {
        id: '3',
        type: 'iris',
        confidence: 0.97,
        features: {
          diameter: '11.5mm',
          texture: '复杂',
          clarity: '清晰',
          pupilRatio: 0.35,
        },
        timestamp: new Date().toLocaleString(),
        status: 'success',
        matchScore: 0.94,
        liveness: true,
      }
    ],
    voice: [
      {
        id: '4',
        type: 'voice',
        confidence: 0.82,
        features: {
          pitch: '中音',
          tone: '平稳',
          accent: '标准普通话',
          duration: '3.2秒',
        },
        timestamp: new Date().toLocaleString(),
        status: 'success',
        matchScore: 0.78,
      }
    ],
    palm: [
      {
        id: '5',
        type: 'palm',
        confidence: 0.91,
        features: {
          palmLines: '清晰',
          palmPrint: '完整',
          veinPattern: '可见',
          handSize: '中等',
        },
        timestamp: new Date().toLocaleString(),
        status: 'success',
        matchScore: 0.87,
      }
    ],
  };

  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      setIsAnalyzing(true);

      // 模拟文件上传和分析
      await new Promise(resolve => setTimeout(resolve, 3000));

      const imageUrl = URL.createObjectURL(file as File);
      const results = mockResults[selectedType] || [];

      const newAnalysis: BiometricAnalysis = {
        id: Date.now().toString(),
        filename: (file as File).name,
        url: imageUrl,
        size: (file as File).size,
        type: selectedType as any,
        results: results,
        timestamp: new Date().toLocaleString(),
        processingTime: 2.5 + Math.random() * 2,
        overallScore: 0.85 + Math.random() * 0.15,
      };

      setAnalyses(prev => [newAnalysis, ...prev]);
      setSelectedAnalysis(newAnalysis);

      onSuccess?.(newAnalysis);
      message.success(`${(file as File).name} 识别完成`);
    } catch (error) {
      onError?.(error as Error);
      message.error('识别失败，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as File);
    }
    setPreviewImage(file.url || file.preview || '');
    setPreviewVisible(true);
  };

  const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 }
      });
      if (cameraRef.current) {
        cameraRef.current.srcObject = stream;
        setIsCameraActive(true);
        message.success('摄像头已启动');
      }
    } catch (error) {
      message.error('无法访问摄像头，请检查权限设置');
    }
  };

  const stopCamera = () => {
    if (cameraRef.current?.srcObject) {
      const stream = cameraRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      cameraRef.current.srcObject = null;
      setIsCameraActive(false);
      message.info('摄像头已关闭');
    }
  };

  const startCapture = async () => {
    setIsCapturing(true);
    setCurrentStep(0);

    // 模拟采集过程
    const steps = ['准备采集', '检测中', '特征提取', '质量评估', '完成'];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCurrentStep(i);
    }

    // 生成模拟结果
    const results = mockResults[selectedType] || [];
    const newAnalysis: BiometricAnalysis = {
      id: Date.now().toString(),
      type: selectedType as any,
      results: results,
      timestamp: new Date().toLocaleString(),
      processingTime: 5,
      overallScore: 0.85 + Math.random() * 0.15,
    };

    setAnalyses(prev => [newAnalysis, ...prev]);
    setSelectedAnalysis(newAnalysis);
    setIsCapturing(false);
    setCurrentStep(0);

    message.success('生物特征采集完成');
  };

  const clearAll = () => {
    setAnalyses([]);
    setSelectedAnalysis(null);
    setFileList([]);
    message.success('已清空所有识别结果');
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = biometricTypes.find(t => t.value === type);
    return typeConfig?.icon || <UserOutlined />;
  };

  const getTypeColor = (type: string) => {
    const typeConfig = biometricTypes.find(t => t.value === type);
    return typeConfig?.color || '#1890ff';
  };

  const getTypeLabel = (type: string) => {
    const typeConfig = biometricTypes.find(t => t.value === type);
    return typeConfig?.label || '未知类型';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'partial':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <ScanOutlined />;
    }
  };

  const captureSteps = [
    { title: '准备采集', description: '初始化采集设备' },
    { title: '检测中', description: '检测生物特征' },
    { title: '特征提取', description: '提取关键特征' },
    { title: '质量评估', description: '评估采集质量' },
    { title: '完成', description: '采集完成' },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
          >
            返回
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          <Card title={
            <Space>
              <UserOutlined />
              <span>生物识别</span>
            </Space>
          }>
            <Tabs defaultActiveKey="upload">
              <Tabs.TabPane tab="文件上传" key="upload">
                <div style={{ marginBottom: 16 }}>
                  <Alert
                    message="选择识别类型"
                    description="请先选择要进行的生物识别类型，然后上传相应的图片或音频文件。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <div style={{ marginBottom: 16 }}>
                    <Text strong>识别类型：</Text>
                    <Select
                      value={selectedType}
                      onChange={setSelectedType}
                      style={{ width: 200, marginLeft: 8 }}
                    >
                      {biometricTypes.map(type => (
                        <Option key={type.value} value={type.value}>
                          <Space>
                            {type.icon}
                            {type.label}
                          </Space>
                        </Option>
                      ))}
                    </Select>
                  </div>
                </div>

                <Upload.Dragger
                  name="file"
                  multiple
                  accept={selectedType === 'voice' ? 'audio/*' : 'image/*'}
                  customRequest={handleUpload}
                  onPreview={handlePreview}
                  fileList={fileList}
                  onChange={({ fileList }) => setFileList(fileList)}
                  disabled={isAnalyzing}
                >
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  </p>
                  <p className="ant-upload-text">
                    点击或拖拽{selectedType === 'voice' ? '音频' : '图片'}文件到此区域上传
                  </p>
                  <p className="ant-upload-hint">
                    支持 {selectedType === 'voice' ? 'MP3、WAV、AAC' : 'JPG、PNG、BMP'} 等格式
                  </p>
                </Upload.Dragger>

                {isAnalyzing && (
                  <div style={{ marginTop: 16, textAlign: 'center' }}>
                    <Progress type="circle" percent={75} />
                    <div style={{ marginTop: 8 }}>
                      <Text>正在进行生物特征识别，请稍候...</Text>
                    </div>
                  </div>
                )}
              </Tabs.TabPane>

              <Tabs.TabPane tab="实时采集" key="capture">
                <div style={{ textAlign: 'center' }}>
                  {selectedType === 'voice' ? (
                    <div>
                      <div style={{ marginBottom: 24 }}>
                        <div style={{
                          width: 200,
                          height: 200,
                          borderRadius: '50%',
                          background: isCapturing ?
                            'radial-gradient(circle, rgba(255,77,79,0.3) 0%, rgba(255,77,79,0.1) 70%)' :
                            '#f5f5f5',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          margin: '0 auto',
                          border: isCapturing ? '3px solid #ff4d4f' : '3px solid #d9d9d9',
                          transition: 'all 0.3s ease',
                        }}>
                          <AudioOutlined style={{
                            fontSize: 64,
                            color: isCapturing ? '#ff4d4f' : '#999',
                          }} />
                        </div>
                      </div>

                      {isCapturing && (
                        <div style={{ marginBottom: 24 }}>
                          <Steps current={currentStep} size="small">
                            {captureSteps.map((step, index) => (
                              <Steps.Step key={index} title={step.title} description={step.description} />
                            ))}
                          </Steps>
                        </div>
                      )}

                      <Space>
                        {!isCapturing ? (
                          <Button
                            type="primary"
                            size="large"
                            icon={<PlayCircleOutlined />}
                            onClick={startCapture}
                          >
                            开始声纹采集
                          </Button>
                        ) : (
                          <Button
                            size="large"
                            danger
                            icon={<StopOutlined />}
                            onClick={() => setIsCapturing(false)}
                          >
                            停止采集
                          </Button>
                        )}
                      </Space>
                    </div>
                  ) : (
                    <div>
                      <div style={{ marginBottom: 16 }}>
                        <video
                          ref={cameraRef}
                          autoPlay
                          playsInline
                          style={{
                            width: '100%',
                            maxWidth: 640,
                            height: 'auto',
                            border: '1px solid #d9d9d9',
                            borderRadius: 6,
                            display: isCameraActive ? 'block' : 'none',
                          }}
                        />
                        <canvas ref={canvasRef} style={{ display: 'none' }} />

                        {!isCameraActive && (
                          <div style={{
                            width: '100%',
                            height: 300,
                            border: '1px dashed #d9d9d9',
                            borderRadius: 6,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            color: '#999',
                          }}>
                            <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                            <Text type="secondary">点击下方按钮启动摄像头</Text>
                          </div>
                        )}
                      </div>

                      {isCapturing && (
                        <div style={{ marginBottom: 24 }}>
                          <Steps current={currentStep} size="small">
                            {captureSteps.map((step, index) => (
                              <Steps.Step key={index} title={step.title} description={step.description} />
                            ))}
                          </Steps>
                        </div>
                      )}

                      <Space>
                        {!isCameraActive ? (
                          <Button
                            type="primary"
                            icon={<CameraOutlined />}
                            onClick={startCamera}
                          >
                            启动摄像头
                          </Button>
                        ) : (
                          <>
                            <Button
                              type="primary"
                              icon={<ScanOutlined />}
                              onClick={startCapture}
                              loading={isCapturing}
                              disabled={isCapturing}
                            >
                              开始采集
                            </Button>
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={stopCamera}
                            >
                              关闭摄像头
                            </Button>
                          </>
                        )}
                      </Space>
                    </div>
                  )}
                </div>
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Col>

        <Col span={8}>
          <Card title="识别设置" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>识别类型</Text>
                <Select
                  value={selectedType}
                  onChange={setSelectedType}
                  style={{ width: '100%', marginTop: 8 }}
                >
                  {biometricTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      <Space>
                        {type.icon}
                        {type.label}
                      </Space>
                    </Option>
                  ))}
                </Select>
                <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                  {biometricTypes.find(t => t.value === selectedType)?.description}
                </div>
              </div>

              <div>
                <Text strong>置信度阈值</Text>
                <Slider
                  value={confidence}
                  onChange={setConfidence}
                  style={{ marginTop: 8 }}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  当前: {confidence}%
                </Text>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>活体检测</Text>
                  <Switch
                    checked={enableLiveness}
                    onChange={setEnableLiveness}
                  />
                </Space>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>防伪检测</Text>
                  <Switch
                    checked={enableAntiSpoofing}
                    onChange={setEnableAntiSpoofing}
                  />
                </Space>
              </div>
            </Space>
          </Card>

          <Card title="类型特性" size="small" style={{ marginTop: 16 }}>
            {biometricTypes.find(t => t.value === selectedType)?.features.map((feature, index) => (
              <Tag key={index} style={{ marginBottom: 8 }}>
                {feature}
              </Tag>
            ))}
          </Card>

          <Card title="统计信息" size="small" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="已识别"
                  value={analyses.length}
                  prefix={<UserOutlined />}
                  suffix="次"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均得分"
                  value={analyses.length > 0 ?
                    (analyses.reduce((sum, a) => sum + a.overallScore, 0) / analyses.length * 100).toFixed(1) :
                    0
                  }
                  suffix="%"
                  prefix={<ScanOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 识别结果 */}
      <Card
        title={
          <Space>
            <UserOutlined />
            <span>识别结果</span>
            <Badge count={analyses.length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => message.info('导出功能开发中')}
            >
              导出
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={clearAll}
              disabled={analyses.length === 0}
            >
              清空
            </Button>
          </Space>
        }
        style={{ marginTop: 24 }}
      >
        {analyses.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <UserOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>暂无识别结果</div>
            <div style={{ fontSize: 12 }}>上传文件或使用实时采集来开始识别</div>
          </div>
        ) : (
          <Row gutter={16}>
            <Col span={8}>
              <List
                size="small"
                dataSource={analyses}
                renderItem={(item) => (
                  <List.Item
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedAnalysis?.id === item.id ? '#f0f0f0' : 'transparent',
                      padding: '8px 12px',
                      borderRadius: 4,
                    }}
                    onClick={() => setSelectedAnalysis(item)}
                  >
                    <List.Item.Meta
                      avatar={
                        <div style={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          backgroundColor: getTypeColor(item.type),
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                        }}>
                          {getTypeIcon(item.type)}
                        </div>
                      }
                      title={
                        <Space>
                          <Text strong style={{ fontSize: 12 }}>
                            {item.filename || `${getTypeLabel(item.type)}识别`}
                          </Text>
                          <Tag color={getTypeColor(item.type)}>
                            {getTypeLabel(item.type)}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            {item.timestamp}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            得分: {(item.overallScore * 100).toFixed(1)}%
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Col>

            <Col span={16}>
              {selectedAnalysis && (
                <div>
                  {selectedAnalysis.url && (
                    <div style={{ marginBottom: 16, textAlign: 'center' }}>
                      <Image
                        src={selectedAnalysis.url}
                        alt={selectedAnalysis.filename}
                        style={{ maxWidth: '100%', maxHeight: 300 }}
                        preview={{
                          mask: (
                            <Space>
                              <ZoomInOutlined />
                              预览
                            </Space>
                          ),
                        }}
                      />
                    </div>
                  )}

                  <Divider orientation="left">识别详情</Divider>

                  <List
                    dataSource={selectedAnalysis.results}
                    renderItem={(result) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={getStatusIcon(result.status)}
                          title={
                            <Space>
                              <Text strong>{getTypeLabel(result.type)}</Text>
                              <Tag color={result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red'}>
                                置信度: {(result.confidence * 100).toFixed(1)}%
                              </Tag>
                              {result.matchScore && (
                                <Tag color="blue">
                                  匹配度: {(result.matchScore * 100).toFixed(1)}%
                                </Tag>
                              )}
                              {result.liveness !== undefined && (
                                <Tag color={result.liveness ? 'green' : 'red'}>
                                  {result.liveness ? '活体' : '非活体'}
                                </Tag>
                              )}
                            </Space>
                          }
                          description={
                            <div>
                              <div style={{ marginBottom: 8 }}>
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                  {result.timestamp}
                                </Text>
                              </div>
                              <div>
                                {Object.entries(result.features).map(([key, value]) => (
                                  <Tag key={key} style={{ marginBottom: 4 }}>
                                    {key}: {value}
                                  </Tag>
                                ))}
                              </div>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </Col>
          </Row>
        )}
      </Card>

      {/* 预览模态框 */}
      <Modal
        open={previewVisible}
        title="图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default BiometricRecognition;
