{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar id = 1;\n\n/**\n * generate unique id in application\n * @return {string}\n */\nvar _default = function _default() {\n  return \"\".concat(id++);\n};\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "id", "_default", "concat"], "sources": ["D:/customerDemo/Link-Agent/frontend/node_modules/size-sensor/lib/id.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar id = 1;\n\n/**\n * generate unique id in application\n * @return {string}\n */\nvar _default = function _default() {\n  return \"\".concat(id++);\n};\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B;AACA;AACA;AACA;;AAEA,IAAIE,EAAE,GAAG,CAAC;;AAEV;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAO,EAAE,CAACC,MAAM,CAACF,EAAE,EAAE,CAAC;AACxB,CAAC;AACDF,OAAO,CAAC,SAAS,CAAC,GAAGG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}