import React, { useState, useRef } from 'react';
import {
  Typo<PERSON>,
  Card,
  Button,
  Space,
  Row,
  Col,
  Upload,
  Image,
  List,
  Tag,
  Progress,
  Alert,
  Tabs,
  Select,
  Switch,
  Slider,
  message,
  Modal,
  Divider,
  Statistic,
  Badge,
  Table,
  Input,
  Collapse,
} from 'antd';
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  UploadOutlined,
  CameraOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ZoomInOutlined,
  ClearOutlined,
  ScanOutlined,
  PictureOutlined,
  TableOutlined,
  IdcardOutlined,
  BankOutlined,
  SafetyCertificateOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  SearchOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { ColumnsType } from 'antd/es/table';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

interface OCRResult {
  id: string;
  text: string;
  confidence: number;
  bbox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  type: 'text' | 'number' | 'date' | 'signature' | 'stamp';
}

interface TableCell {
  row: number;
  col: number;
  text: string;
  confidence: number;
}

interface DocumentAnalysis {
  id: string;
  filename: string;
  url: string;
  size: number;
  type: 'id_card' | 'passport' | 'invoice' | 'contract' | 'receipt' | 'bank_card' | 'license' | 'certificate' | 'general';
  ocrResults: OCRResult[];
  tableData?: TableCell[][];
  extractedFields?: Record<string, any>;
  timestamp: string;
  processingTime: number;
  confidence: number;
}

const DocumentRecognition: React.FC = () => {
  const navigate = useNavigate();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [analyses, setAnalyses] = useState<DocumentAnalysis[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['text', 'table', 'fields']);
  const [confidence, setConfidence] = useState(70);
  const [enableStructure, setEnableStructure] = useState(true);
  const [enableTable, setEnableTable] = useState(true);
  const [language, setLanguage] = useState('zh-CN');

  const cameraRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  // 文档类型配置
  const documentTypes = [
    { value: 'id_card', label: '身份证', icon: <IdcardOutlined />, color: '#1890ff' },
    { value: 'passport', label: '护照', icon: <SafetyCertificateOutlined />, color: '#52c41a' },
    { value: 'invoice', label: '发票', icon: <FileTextOutlined />, color: '#faad14' },
    { value: 'contract', label: '合同', icon: <FilePdfOutlined />, color: '#722ed1' },
    { value: 'receipt', label: '收据', icon: <FileImageOutlined />, color: '#eb2f96' },
    { value: 'bank_card', label: '银行卡', icon: <BankOutlined />, color: '#13c2c2' },
    { value: 'license', label: '证照', icon: <SafetyCertificateOutlined />, color: '#fa8c16' },
    { value: 'certificate', label: '证书', icon: <SafetyCertificateOutlined />, color: '#a0d911' },
    { value: 'general', label: '通用文档', icon: <FileTextOutlined />, color: '#666' },
  ];

  // 语言选项
  const languageOptions = [
    { value: 'zh-CN', label: '中文（简体）' },
    { value: 'zh-TW', label: '中文（繁体）' },
    { value: 'en-US', label: 'English' },
    { value: 'ja-JP', label: '日本語' },
    { value: 'ko-KR', label: '한국어' },
  ];

  // 模拟OCR结果
  const mockOCRResults: OCRResult[] = [
    {
      id: '1',
      text: '中华人民共和国居民身份证',
      confidence: 0.98,
      bbox: { x: 50, y: 20, width: 300, height: 25 },
      type: 'text',
    },
    {
      id: '2',
      text: '张三',
      confidence: 0.95,
      bbox: { x: 120, y: 80, width: 60, height: 20 },
      type: 'text',
    },
    {
      id: '3',
      text: '男',
      confidence: 0.92,
      bbox: { x: 120, y: 110, width: 20, height: 20 },
      type: 'text',
    },
    {
      id: '4',
      text: '1990年01月01日',
      confidence: 0.94,
      bbox: { x: 120, y: 140, width: 100, height: 20 },
      type: 'date',
    },
    {
      id: '5',
      text: '110101199001010001',
      confidence: 0.96,
      bbox: { x: 120, y: 170, width: 150, height: 20 },
      type: 'number',
    },
  ];

  // 模拟提取字段
  const mockExtractedFields = {
    name: '张三',
    gender: '男',
    birthDate: '1990年01月01日',
    idNumber: '110101199001010001',
    address: '北京市东城区某某街道某某号',
    issueDate: '2020年01月01日',
    expiryDate: '2030年01月01日',
    issuingAuthority: '北京市公安局东城分局',
  };

  // 模拟表格数据
  const mockTableData: TableCell[][] = [
    [
      { row: 0, col: 0, text: '项目', confidence: 0.95 },
      { row: 0, col: 1, text: '数量', confidence: 0.93 },
      { row: 0, col: 2, text: '单价', confidence: 0.94 },
      { row: 0, col: 3, text: '金额', confidence: 0.96 },
    ],
    [
      { row: 1, col: 0, text: '办公用品', confidence: 0.92 },
      { row: 1, col: 1, text: '10', confidence: 0.98 },
      { row: 1, col: 2, text: '50.00', confidence: 0.97 },
      { row: 1, col: 3, text: '500.00', confidence: 0.95 },
    ],
    [
      { row: 2, col: 0, text: '电子设备', confidence: 0.94 },
      { row: 2, col: 1, text: '2', confidence: 0.99 },
      { row: 2, col: 2, text: '1200.00', confidence: 0.96 },
      { row: 2, col: 3, text: '2400.00', confidence: 0.98 },
    ],
  ];

  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      setIsAnalyzing(true);

      // 模拟文件上传和分析
      await new Promise(resolve => setTimeout(resolve, 3000));

      const imageUrl = URL.createObjectURL(file as File);
      const newAnalysis: DocumentAnalysis = {
        id: Date.now().toString(),
        filename: (file as File).name,
        url: imageUrl,
        size: (file as File).size,
        type: 'id_card', // 模拟识别为身份证
        ocrResults: mockOCRResults,
        tableData: enableTable ? mockTableData : undefined,
        extractedFields: mockExtractedFields,
        timestamp: new Date().toLocaleString(),
        processingTime: 2.5 + Math.random() * 2,
        confidence: 0.85 + Math.random() * 0.15,
      };

      setAnalyses(prev => [newAnalysis, ...prev]);
      setSelectedDocument(newAnalysis);

      onSuccess?.(newAnalysis);
      message.success(`${(file as File).name} 识别完成`);
    } catch (error) {
      onError?.(error as Error);
      message.error('识别失败，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as File);
    }
    setPreviewImage(file.url || file.preview || '');
    setPreviewVisible(true);
  };

  const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 }
      });
      if (cameraRef.current) {
        cameraRef.current.srcObject = stream;
        setIsCameraActive(true);
        message.success('摄像头已启动');
      }
    } catch (error) {
      message.error('无法访问摄像头，请检查权限设置');
    }
  };

  const stopCamera = () => {
    if (cameraRef.current?.srcObject) {
      const stream = cameraRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      cameraRef.current.srcObject = null;
      setIsCameraActive(false);
      message.info('摄像头已关闭');
    }
  };

  const capturePhoto = () => {
    if (cameraRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = cameraRef.current;
      const context = canvas.getContext('2d');

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context?.drawImage(video, 0, 0);

      canvas.toBlob(blob => {
        if (blob) {
          const file = new File([blob], `document_${Date.now()}.jpg`, { type: 'image/jpeg' });
          handleUpload({
            file,
            onSuccess: () => {},
            onError: () => {},
          } as any);
        }
      });
    }
  };

  const clearAll = () => {
    setAnalyses([]);
    setSelectedDocument(null);
    setFileList([]);
    message.success('已清空所有识别结果');
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = documentTypes.find(t => t.value === type);
    return typeConfig?.icon || <FileTextOutlined />;
  };

  const getTypeColor = (type: string) => {
    const typeConfig = documentTypes.find(t => t.value === type);
    return typeConfig?.color || '#1890ff';
  };

  const getTypeLabel = (type: string) => {
    const typeConfig = documentTypes.find(t => t.value === type);
    return typeConfig?.label || '未知类型';
  };

  // 表格列定义
  const tableColumns: ColumnsType<any> = mockTableData[0]?.map((_, colIndex) => ({
    title: `列 ${colIndex + 1}`,
    dataIndex: colIndex,
    key: colIndex,
    render: (text: string, record: any, rowIndex: number) => {
      const cell = mockTableData[rowIndex]?.[colIndex];
      return (
        <div>
          <div>{cell?.text}</div>
          <div style={{ fontSize: 11, color: '#999' }}>
            置信度: {((cell?.confidence || 0) * 100).toFixed(1)}%
          </div>
        </div>
      );
    },
  })) || [];

  const tableDataSource = mockTableData.map((row, index) => ({
    key: index,
    ...row.reduce((acc, cell, colIndex) => {
      acc[colIndex] = cell.text;
      return acc;
    }, {} as Record<number, string>),
  }));

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
          >
            返回
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          <Card title={
            <Space>
              <FileTextOutlined />
              <span>文档识别</span>
            </Space>
          }>
            <Tabs defaultActiveKey="upload">
              <Tabs.TabPane tab="文件上传" key="upload">
                <Upload.Dragger
                  name="file"
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  customRequest={handleUpload}
                  onPreview={handlePreview}
                  fileList={fileList}
                  onChange={({ fileList }) => setFileList(fileList)}
                  disabled={isAnalyzing}
                >
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文档到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持 JPG、PNG、PDF、DOC、DOCX 等格式，单个文件不超过 20MB
                  </p>
                </Upload.Dragger>

                {isAnalyzing && (
                  <div style={{ marginTop: 16, textAlign: 'center' }}>
                    <Progress type="circle" percent={75} />
                    <div style={{ marginTop: 8 }}>
                      <Text>正在识别文档，请稍候...</Text>
                    </div>
                  </div>
                )}
              </Tabs.TabPane>

              <Tabs.TabPane tab="摄像头拍照" key="camera">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ marginBottom: 16 }}>
                    <video
                      ref={cameraRef}
                      autoPlay
                      playsInline
                      style={{
                        width: '100%',
                        maxWidth: 640,
                        height: 'auto',
                        border: '1px solid #d9d9d9',
                        borderRadius: 6,
                        display: isCameraActive ? 'block' : 'none',
                      }}
                    />
                    <canvas ref={canvasRef} style={{ display: 'none' }} />

                    {!isCameraActive && (
                      <div style={{
                        width: '100%',
                        height: 300,
                        border: '1px dashed #d9d9d9',
                        borderRadius: 6,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: '#999',
                      }}>
                        <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                        <Text type="secondary">点击下方按钮启动摄像头</Text>
                      </div>
                    )}
                  </div>

                  <Space>
                    {!isCameraActive ? (
                      <Button
                        type="primary"
                        icon={<CameraOutlined />}
                        onClick={startCamera}
                      >
                        启动摄像头
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="primary"
                          icon={<CameraOutlined />}
                          onClick={capturePhoto}
                          loading={isAnalyzing}
                        >
                          拍照识别
                        </Button>
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={stopCamera}
                        >
                          关闭摄像头
                        </Button>
                      </>
                    )}
                  </Space>
                </div>
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Col>

        <Col span={8}>
          <Card title="识别设置" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>识别语言</Text>
                <Select
                  value={language}
                  onChange={setLanguage}
                  style={{ width: '100%', marginTop: 8 }}
                >
                  {languageOptions.map(lang => (
                    <Option key={lang.value} value={lang.value}>
                      {lang.label}
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <Text strong>置信度阈值</Text>
                <Slider
                  value={confidence}
                  onChange={setConfidence}
                  style={{ marginTop: 8 }}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  当前: {confidence}%
                </Text>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>结构化识别</Text>
                  <Switch
                    checked={enableStructure}
                    onChange={setEnableStructure}
                  />
                </Space>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>表格识别</Text>
                  <Switch
                    checked={enableTable}
                    onChange={setEnableTable}
                  />
                </Space>
              </div>
            </Space>
          </Card>

          <Card title="统计信息" size="small" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="已识别"
                  value={analyses.length}
                  prefix={<FileTextOutlined />}
                  suffix="份"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均准确率"
                  value={analyses.length > 0 ?
                    (analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length * 100).toFixed(1) :
                    0
                  }
                  suffix="%"
                  prefix={<ScanOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 识别结果 */}
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>识别结果</span>
            <Badge count={analyses.length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => message.info('导出功能开发中')}
            >
              导出
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={clearAll}
              disabled={analyses.length === 0}
            >
              清空
            </Button>
          </Space>
        }
        style={{ marginTop: 24 }}
      >
        {analyses.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>暂无识别结果</div>
            <div style={{ fontSize: 12 }}>上传文档或使用摄像头拍照来开始识别</div>
          </div>
        ) : (
          <Row gutter={16}>
            <Col span={8}>
              <List
                size="small"
                dataSource={analyses}
                renderItem={(item) => (
                  <List.Item
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedDocument?.id === item.id ? '#f0f0f0' : 'transparent',
                      padding: '8px 12px',
                      borderRadius: 4,
                    }}
                    onClick={() => setSelectedDocument(item)}
                  >
                    <List.Item.Meta
                      avatar={
                        <div style={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          backgroundColor: getTypeColor(item.type),
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                        }}>
                          {getTypeIcon(item.type)}
                        </div>
                      }
                      title={
                        <Space>
                          <Text strong style={{ fontSize: 12 }}>
                            {item.filename}
                          </Text>
                          <Tag color={getTypeColor(item.type)}>
                            {getTypeLabel(item.type)}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            {item.timestamp}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            准确率: {(item.confidence * 100).toFixed(1)}%
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Col>

            <Col span={16}>
              {selectedDocument && (
                <div>
                  <div style={{ marginBottom: 16, textAlign: 'center' }}>
                    <Image
                      src={selectedDocument.url}
                      alt={selectedDocument.filename}
                      style={{ maxWidth: '100%', maxHeight: 300 }}
                      preview={{
                        mask: (
                          <Space>
                            <ZoomInOutlined />
                            预览
                          </Space>
                        ),
                      }}
                    />
                  </div>

                  <Tabs defaultActiveKey="ocr">
                    <Tabs.TabPane tab="OCR文字" key="ocr">
                      <List
                        size="small"
                        dataSource={selectedDocument.ocrResults}
                        renderItem={(result) => (
                          <List.Item
                            actions={[
                              <Button
                                key="copy"
                                type="text"
                                size="small"
                                icon={<CopyOutlined />}
                                onClick={() => {
                                  navigator.clipboard.writeText(result.text);
                                  message.success('已复制到剪贴板');
                                }}
                              />
                            ]}
                          >
                            <List.Item.Meta
                              title={
                                <Space>
                                  <Text>{result.text}</Text>
                                  <Tag color={result.confidence >= 0.9 ? 'green' : result.confidence >= 0.7 ? 'orange' : 'red'}>
                                    {(result.confidence * 100).toFixed(1)}%
                                  </Tag>
                                  <Tag color="blue">{result.type}</Tag>
                                </Space>
                              }
                              description={
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                  位置: ({result.bbox.x}, {result.bbox.y})
                                  尺寸: {result.bbox.width}×{result.bbox.height}
                                </Text>
                              }
                            />
                          </List.Item>
                        )}
                      />
                    </Tabs.TabPane>

                    {selectedDocument.extractedFields && (
                      <Tabs.TabPane tab="结构化字段" key="fields">
                        <Collapse defaultActiveKey={['1']}>
                          <Panel header="提取字段" key="1">
                            <Row gutter={[16, 16]}>
                              {Object.entries(selectedDocument.extractedFields).map(([key, value]) => (
                                <Col span={12} key={key}>
                                  <div style={{
                                    padding: 12,
                                    border: '1px solid #d9d9d9',
                                    borderRadius: 4,
                                    backgroundColor: '#fafafa',
                                  }}>
                                    <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                                      {key}
                                    </div>
                                    <div style={{ fontWeight: 500 }}>
                                      {value}
                                    </div>
                                  </div>
                                </Col>
                              ))}
                            </Row>
                          </Panel>
                        </Collapse>
                      </Tabs.TabPane>
                    )}

                    {selectedDocument.tableData && (
                      <Tabs.TabPane tab="表格数据" key="table">
                        <Table
                          columns={tableColumns}
                          dataSource={tableDataSource}
                          size="small"
                          pagination={false}
                          bordered
                        />
                      </Tabs.TabPane>
                    )}
                  </Tabs>
                </div>
              )}
            </Col>
          </Row>
        )}
      </Card>

      {/* 预览模态框 */}
      <Modal
        open={previewVisible}
        title="文档预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default DocumentRecognition;
