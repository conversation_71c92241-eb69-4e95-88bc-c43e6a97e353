{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Agent\\\\AgentCreate.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Typography, Card, Form, Input, Select, Button, Space, Steps } from 'antd';\nimport { ArrowLeftOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { ROUTES } from '../../utils/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Step\n} = Steps;\nconst AgentCreate = () => {\n  _s();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [currentStep, setCurrentStep] = useState(0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(ROUTES.AGENTS),\n          children: \"\\u8FD4\\u56DE\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n          style: {\n            fontSize: 64,\n            color: '#1890ff',\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u521B\\u5EFA\\u667A\\u80FD\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          type: \"secondary\",\n          children: \"\\u667A\\u80FD\\u4F53\\u521B\\u5EFA\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: 24\n          },\n          children: \"\\u8FD9\\u91CC\\u5C06\\u63D0\\u4F9B\\u5B8C\\u6574\\u7684\\u667A\\u80FD\\u4F53\\u521B\\u5EFA\\u5411\\u5BFC\\uFF0C\\u5305\\u62EC\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            textAlign: 'left',\n            display: 'inline-block',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u57FA\\u672C\\u4FE1\\u606F\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u6A21\\u578B\\u9009\\u62E9\\u548C\\u53C2\\u6570\\u8C03\\u4F18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u80FD\\u529B\\u548C\\u6280\\u80FD\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u9AD8\\u7EA7\\u8BBE\\u7F6E\\u548C\\u90E8\\u7F72\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentCreate, \"xi+CL9VCkWfIzuf+L+0SFA5R608=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = AgentCreate;\nexport default AgentCreate;\nvar _c;\n$RefreshReg$(_c, \"AgentCreate\");", "map": {"version": 3, "names": ["React", "useState", "Typography", "Card", "Form", "Input", "Select", "<PERSON><PERSON>", "Space", "Steps", "ArrowLeftOutlined", "RobotOutlined", "useNavigate", "ROUTES", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "TextArea", "Option", "Step", "AgentCreate", "_s", "navigate", "form", "useForm", "loading", "setLoading", "currentStep", "setCurrentStep", "children", "style", "marginBottom", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "AGENTS", "textAlign", "padding", "fontSize", "color", "level", "type", "display", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Agent/AgentCreate.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Typography,\n  Card,\n  Form,\n  Input,\n  Select,\n  Button,\n  Space,\n  Row,\n  Col,\n  InputNumber,\n  Divider,\n  message,\n  Steps,\n} from 'antd';\nimport { ArrowLeftOutlined, SaveOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { ROUTES } from '../../utils/constants';\n\nconst { Title, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Step } = Steps;\n\ninterface AgentFormData {\n  name: string;\n  description: string;\n  type: 'chatbot' | 'workflow' | 'recognition' | 'analysis';\n  model: string;\n  capabilities: string[];\n  config: {\n    maxTokens?: number;\n    temperature?: number;\n    timeout?: number;\n    retryCount?: number;\n  };\n}\n\nconst AgentCreate: React.FC = () => {\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [currentStep, setCurrentStep] = useState(0);\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(ROUTES.AGENTS)}\n          >\n            返回列表\n          </Button>\n        </Space>\n      </div>\n\n      <Card>\n        <div style={{ textAlign: 'center', padding: '40px 0' }}>\n          <RobotOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />\n          <Title level={2}>\n            创建智能体\n          </Title>\n          <Paragraph type=\"secondary\">\n            智能体创建功能正在开发中，敬请期待...\n          </Paragraph>\n          <p style={{ color: '#666', marginBottom: 24 }}>\n            这里将提供完整的智能体创建向导，包括：\n          </p>\n          <ul style={{ textAlign: 'left', display: 'inline-block', color: '#666' }}>\n            <li>基本信息配置</li>\n            <li>模型选择和参数调优</li>\n            <li>能力和技能配置</li>\n            <li>高级设置和部署选项</li>\n          </ul>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AgentCreate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EAMLC,KAAK,QACA,MAAM;AACb,SAASC,iBAAiB,EAAgBC,aAAa,QAAQ,mBAAmB;AAClF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGf,UAAU;AACvC,MAAM;EAAEgB;AAAS,CAAC,GAAGb,KAAK;AAC1B,MAAM;EAAEc;AAAO,CAAC,GAAGb,MAAM;AACzB,MAAM;EAAEc;AAAK,CAAC,GAAGX,KAAK;AAgBtB,MAAMY,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,CAAC,GAAGpB,IAAI,CAACqB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAEjD,oBACEc,OAAA;IAAAe,QAAA,gBACEf,OAAA;MAAKgB,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAF,QAAA,eAC/Bf,OAAA,CAACP,KAAK;QAAAsB,QAAA,eACJf,OAAA,CAACR,MAAM;UACL0B,IAAI,eAAElB,OAAA,CAACL,iBAAiB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAEA,CAAA,KAAMf,QAAQ,CAACV,MAAM,CAAC0B,MAAM,CAAE;UAAAT,QAAA,EACxC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENtB,OAAA,CAACZ,IAAI;MAAA2B,QAAA,eACHf,OAAA;QAAKgB,KAAK,EAAE;UAAES,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAX,QAAA,gBACrDf,OAAA,CAACJ,aAAa;UAACoB,KAAK,EAAE;YAAEW,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEX,YAAY,EAAE;UAAG;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EtB,OAAA,CAACC,KAAK;UAAC4B,KAAK,EAAE,CAAE;UAAAd,QAAA,EAAC;QAEjB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtB,OAAA,CAACE,SAAS;UAAC4B,IAAI,EAAC,WAAW;UAAAf,QAAA,EAAC;QAE5B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZtB,OAAA;UAAGgB,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEX,YAAY,EAAE;UAAG,CAAE;UAAAF,QAAA,EAAC;QAE/C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtB,OAAA;UAAIgB,KAAK,EAAE;YAAES,SAAS,EAAE,MAAM;YAAEM,OAAO,EAAE,cAAc;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACvEf,OAAA;YAAAe,QAAA,EAAI;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACftB,OAAA;YAAAe,QAAA,EAAI;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBtB,OAAA;YAAAe,QAAA,EAAI;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBtB,OAAA;YAAAe,QAAA,EAAI;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACf,EAAA,CAzCID,WAAqB;EAAA,QACRT,WAAW,EACbR,IAAI,CAACqB,OAAO;AAAA;AAAAsB,EAAA,GAFvB1B,WAAqB;AA2C3B,eAAeA,WAAW;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}