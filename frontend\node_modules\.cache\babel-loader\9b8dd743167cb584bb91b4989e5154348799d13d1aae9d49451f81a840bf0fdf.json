{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Agent\\\\AgentCreate.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Typography, Card, Form, Input, Select, Button, Space, Row, Col, InputNumber, Divider, message, Steps } from 'antd';\nimport { ArrowLeftOutlined, SaveOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { ROUTES } from '../../utils/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Step\n} = Steps;\nconst AgentCreate = () => {\n  _s();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [currentStep, setCurrentStep] = useState(0);\n  const agentTypes = [{\n    value: 'chatbot',\n    label: '聊天机器人',\n    description: '处理对话和问答',\n    icon: '💬'\n  }, {\n    value: 'workflow',\n    label: '工作流助手',\n    description: '自动化业务流程',\n    icon: '🔄'\n  }, {\n    value: 'recognition',\n    label: '识别助手',\n    description: '图像、语音、文档识别',\n    icon: '👁️'\n  }, {\n    value: 'analysis',\n    label: '分析助手',\n    description: '数据分析和洞察',\n    icon: '📊'\n  }];\n  const modelOptions = [{\n    value: 'GPT-4',\n    label: 'GPT-4',\n    description: '最强大的语言模型'\n  }, {\n    value: 'GPT-3.5',\n    label: 'GPT-3.5 Turbo',\n    description: '快速且经济的选择'\n  }, {\n    value: 'Claude-3',\n    label: 'Claude-3',\n    description: '擅长分析和推理'\n  }, {\n    value: 'Gemini-Pro',\n    label: 'Gemini Pro',\n    description: '多模态能力强'\n  }];\n  const capabilityOptions = ['自然语言理解', '情感分析', '多轮对话', '知识问答', '文档解析', '内容生成', '代码生成', '数据分析', '图像识别', '语音识别', '文本翻译', '摘要生成'];\n  const handleSubmit = async values => {\n    setLoading(true);\n    try {\n      // 模拟创建智能体\n      console.log('Creating agent:', values);\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      message.success('智能体创建成功');\n      navigate(ROUTES.AGENTS);\n    } catch (error) {\n      message.error('创建失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleNext = () => {\n    form.validateFields().then(() => {\n      setCurrentStep(currentStep + 1);\n    });\n  };\n  const handlePrev = () => {\n    setCurrentStep(currentStep - 1);\n  };\n  const steps = [{\n    title: '基本信息',\n    content: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u667A\\u80FD\\u4F53\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入智能体名称'\n          }, {\n            min: 2,\n            max: 50,\n            message: '名称长度应在2-50个字符之间'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u667A\\u80FD\\u4F53\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入描述'\n          }, {\n            max: 200,\n            message: '描述不能超过200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u667A\\u80FD\\u4F53\\u7684\\u529F\\u80FD\\u548C\\u7528\\u9014\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u667A\\u80FD\\u4F53\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择智能体类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u667A\\u80FD\\u4F53\\u7C7B\\u578B\",\n            children: agentTypes.map(type => /*#__PURE__*/_jsxDEV(Option, {\n              value: type.value,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: type.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: type.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: 12,\n                      color: '#666'\n                    },\n                    children: type.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 21\n              }, this)\n            }, type.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '模型配置',\n    content: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model\",\n          label: \"\\u57FA\\u7840\\u6A21\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择基础模型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u57FA\\u7840\\u6A21\\u578B\",\n            children: modelOptions.map(model => /*#__PURE__*/_jsxDEV(Option, {\n              value: model.value,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: model.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: 12,\n                    color: '#666'\n                  },\n                  children: model.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)\n            }, model.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"capabilities\",\n          label: \"\\u80FD\\u529B\\u914D\\u7F6E\",\n          rules: [{\n            required: true,\n            message: '请选择至少一个能力'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u667A\\u80FD\\u4F53\\u7684\\u80FD\\u529B\",\n            style: {\n              width: '100%'\n            },\n            children: capabilityOptions.map(capability => /*#__PURE__*/_jsxDEV(Option, {\n              value: capability,\n              children: capability\n            }, capability, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '高级配置',\n    content: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: ['config', 'maxTokens'],\n          label: \"\\u6700\\u5927Token\\u6570\",\n          tooltip: \"\\u5355\\u6B21\\u8BF7\\u6C42\\u7684\\u6700\\u5927Token\\u6570\\u91CF\",\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 100,\n            max: 8192,\n            placeholder: \"2048\",\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: ['config', 'temperature'],\n          label: \"\\u521B\\u9020\\u6027\",\n          tooltip: \"\\u63A7\\u5236\\u8F93\\u51FA\\u7684\\u968F\\u673A\\u6027\\uFF0C0-1\\u4E4B\\u95F4\",\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 0,\n            max: 1,\n            step: 0.1,\n            placeholder: \"0.7\",\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: ['config', 'timeout'],\n          label: \"\\u8D85\\u65F6\\u65F6\\u95F4(\\u79D2)\",\n          tooltip: \"\\u8BF7\\u6C42\\u8D85\\u65F6\\u65F6\\u95F4\",\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 5,\n            max: 300,\n            placeholder: \"30\",\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: ['config', 'retryCount'],\n          label: \"\\u91CD\\u8BD5\\u6B21\\u6570\",\n          tooltip: \"\\u5931\\u8D25\\u65F6\\u7684\\u91CD\\u8BD5\\u6B21\\u6570\",\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 0,\n            max: 5,\n            placeholder: \"3\",\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(ROUTES.AGENTS),\n          children: \"\\u8FD4\\u56DE\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            margin: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), \"\\u521B\\u5EFA\\u667A\\u80FD\\u4F53\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          type: \"secondary\",\n          children: \"\\u914D\\u7F6E\\u60A8\\u7684\\u667A\\u80FD\\u4F53\\uFF0C\\u5305\\u62EC\\u57FA\\u672C\\u4FE1\\u606F\\u3001\\u6A21\\u578B\\u9009\\u62E9\\u548C\\u9AD8\\u7EA7\\u53C2\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Steps, {\n        current: currentStep,\n        style: {\n          marginBottom: 32\n        },\n        children: steps.map(item => /*#__PURE__*/_jsxDEV(Step, {\n          title: item.title\n        }, item.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          config: {\n            maxTokens: 2048,\n            temperature: 0.7,\n            timeout: 30,\n            retryCount: 3\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            minHeight: 400\n          },\n          children: steps[currentStep].content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [currentStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handlePrev,\n              children: \"\\u4E0A\\u4E00\\u6B65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this), currentStep < steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: handleNext,\n              children: \"\\u4E0B\\u4E00\\u6B65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), currentStep === steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 25\n              }, this),\n              children: \"\\u521B\\u5EFA\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 291,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentCreate, \"xi+CL9VCkWfIzuf+L+0SFA5R608=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = AgentCreate;\nexport default AgentCreate;\nvar _c;\n$RefreshReg$(_c, \"AgentCreate\");", "map": {"version": 3, "names": ["React", "useState", "Typography", "Card", "Form", "Input", "Select", "<PERSON><PERSON>", "Space", "Row", "Col", "InputNumber", "Divider", "message", "Steps", "ArrowLeftOutlined", "SaveOutlined", "RobotOutlined", "useNavigate", "ROUTES", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "TextArea", "Option", "Step", "AgentCreate", "_s", "navigate", "form", "useForm", "loading", "setLoading", "currentStep", "setCurrentStep", "agentTypes", "value", "label", "description", "icon", "modelOptions", "capabilityOptions", "handleSubmit", "values", "console", "log", "Promise", "resolve", "setTimeout", "success", "AGENTS", "error", "handleNext", "validateFields", "then", "handlePrev", "steps", "title", "content", "gutter", "children", "span", "<PERSON><PERSON>", "name", "rules", "required", "min", "max", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rows", "map", "type", "style", "fontSize", "color", "model", "mode", "width", "capability", "tooltip", "step", "marginBottom", "onClick", "level", "margin", "marginRight", "current", "item", "layout", "onFinish", "initialValues", "config", "maxTokens", "temperature", "timeout", "retryCount", "minHeight", "textAlign", "length", "htmlType", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Agent/AgentCreate.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Typography,\n  Card,\n  Form,\n  Input,\n  Select,\n  Button,\n  Space,\n  Row,\n  Col,\n  InputNumber,\n  Divider,\n  message,\n  Steps,\n} from 'antd';\nimport { ArrowLeftOutlined, SaveOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { ROUTES } from '../../utils/constants';\n\nconst { Title, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Step } = Steps;\n\ninterface AgentFormData {\n  name: string;\n  description: string;\n  type: 'chatbot' | 'workflow' | 'recognition' | 'analysis';\n  model: string;\n  capabilities: string[];\n  config: {\n    maxTokens?: number;\n    temperature?: number;\n    timeout?: number;\n    retryCount?: number;\n  };\n}\n\nconst AgentCreate: React.FC = () => {\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [currentStep, setCurrentStep] = useState(0);\n\n  const agentTypes = [\n    {\n      value: 'chatbot',\n      label: '聊天机器人',\n      description: '处理对话和问答',\n      icon: '💬',\n    },\n    {\n      value: 'workflow',\n      label: '工作流助手',\n      description: '自动化业务流程',\n      icon: '🔄',\n    },\n    {\n      value: 'recognition',\n      label: '识别助手',\n      description: '图像、语音、文档识别',\n      icon: '👁️',\n    },\n    {\n      value: 'analysis',\n      label: '分析助手',\n      description: '数据分析和洞察',\n      icon: '📊',\n    },\n  ];\n\n  const modelOptions = [\n    { value: 'GPT-4', label: 'GPT-4', description: '最强大的语言模型' },\n    { value: 'GPT-3.5', label: 'GPT-3.5 Turbo', description: '快速且经济的选择' },\n    { value: 'Claude-3', label: 'Claude-3', description: '擅长分析和推理' },\n    { value: 'Gemini-Pro', label: 'Gemini Pro', description: '多模态能力强' },\n  ];\n\n  const capabilityOptions = [\n    '自然语言理解',\n    '情感分析',\n    '多轮对话',\n    '知识问答',\n    '文档解析',\n    '内容生成',\n    '代码生成',\n    '数据分析',\n    '图像识别',\n    '语音识别',\n    '文本翻译',\n    '摘要生成',\n  ];\n\n  const handleSubmit = async (values: AgentFormData) => {\n    setLoading(true);\n    try {\n      // 模拟创建智能体\n      console.log('Creating agent:', values);\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      message.success('智能体创建成功');\n      navigate(ROUTES.AGENTS);\n    } catch (error) {\n      message.error('创建失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleNext = () => {\n    form.validateFields().then(() => {\n      setCurrentStep(currentStep + 1);\n    });\n  };\n\n  const handlePrev = () => {\n    setCurrentStep(currentStep - 1);\n  };\n\n  const steps = [\n    {\n      title: '基本信息',\n      content: (\n        <Row gutter={24}>\n          <Col span={24}>\n            <Form.Item\n              name=\"name\"\n              label=\"智能体名称\"\n              rules={[\n                { required: true, message: '请输入智能体名称' },\n                { min: 2, max: 50, message: '名称长度应在2-50个字符之间' },\n              ]}\n            >\n              <Input placeholder=\"请输入智能体名称\" />\n            </Form.Item>\n          </Col>\n          <Col span={24}>\n            <Form.Item\n              name=\"description\"\n              label=\"描述\"\n              rules={[\n                { required: true, message: '请输入描述' },\n                { max: 200, message: '描述不能超过200个字符' },\n              ]}\n            >\n              <TextArea\n                rows={3}\n                placeholder=\"请描述智能体的功能和用途\"\n              />\n            </Form.Item>\n          </Col>\n          <Col span={24}>\n            <Form.Item\n              name=\"type\"\n              label=\"智能体类型\"\n              rules={[{ required: true, message: '请选择智能体类型' }]}\n            >\n              <Select placeholder=\"请选择智能体类型\">\n                {agentTypes.map(type => (\n                  <Option key={type.value} value={type.value}>\n                    <Space>\n                      <span>{type.icon}</span>\n                      <div>\n                        <div>{type.label}</div>\n                        <div style={{ fontSize: 12, color: '#666' }}>\n                          {type.description}\n                        </div>\n                      </div>\n                    </Space>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n      ),\n    },\n    {\n      title: '模型配置',\n      content: (\n        <Row gutter={24}>\n          <Col span={24}>\n            <Form.Item\n              name=\"model\"\n              label=\"基础模型\"\n              rules={[{ required: true, message: '请选择基础模型' }]}\n            >\n              <Select placeholder=\"请选择基础模型\">\n                {modelOptions.map(model => (\n                  <Option key={model.value} value={model.value}>\n                    <div>\n                      <div>{model.label}</div>\n                      <div style={{ fontSize: 12, color: '#666' }}>\n                        {model.description}\n                      </div>\n                    </div>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n          <Col span={24}>\n            <Form.Item\n              name=\"capabilities\"\n              label=\"能力配置\"\n              rules={[{ required: true, message: '请选择至少一个能力' }]}\n            >\n              <Select\n                mode=\"multiple\"\n                placeholder=\"请选择智能体的能力\"\n                style={{ width: '100%' }}\n              >\n                {capabilityOptions.map(capability => (\n                  <Option key={capability} value={capability}>\n                    {capability}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n      ),\n    },\n    {\n      title: '高级配置',\n      content: (\n        <Row gutter={24}>\n          <Col span={12}>\n            <Form.Item\n              name={['config', 'maxTokens']}\n              label=\"最大Token数\"\n              tooltip=\"单次请求的最大Token数量\"\n            >\n              <InputNumber\n                min={100}\n                max={8192}\n                placeholder=\"2048\"\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item\n              name={['config', 'temperature']}\n              label=\"创造性\"\n              tooltip=\"控制输出的随机性，0-1之间\"\n            >\n              <InputNumber\n                min={0}\n                max={1}\n                step={0.1}\n                placeholder=\"0.7\"\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item\n              name={['config', 'timeout']}\n              label=\"超时时间(秒)\"\n              tooltip=\"请求超时时间\"\n            >\n              <InputNumber\n                min={5}\n                max={300}\n                placeholder=\"30\"\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n          <Col span={12}>\n            <Form.Item\n              name={['config', 'retryCount']}\n              label=\"重试次数\"\n              tooltip=\"失败时的重试次数\"\n            >\n              <InputNumber\n                min={0}\n                max={5}\n                placeholder=\"3\"\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(ROUTES.AGENTS)}\n          >\n            返回列表\n          </Button>\n        </Space>\n      </div>\n\n      <Card>\n        <div style={{ marginBottom: 24 }}>\n          <Title level={2} style={{ margin: 0 }}>\n            <RobotOutlined style={{ marginRight: 8 }} />\n            创建智能体\n          </Title>\n          <Paragraph type=\"secondary\">\n            配置您的智能体，包括基本信息、模型选择和高级参数\n          </Paragraph>\n        </div>\n\n        <Steps current={currentStep} style={{ marginBottom: 32 }}>\n          {steps.map(item => (\n            <Step key={item.title} title={item.title} />\n          ))}\n        </Steps>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            config: {\n              maxTokens: 2048,\n              temperature: 0.7,\n              timeout: 30,\n              retryCount: 3,\n            },\n          }}\n        >\n          <div style={{ minHeight: 400 }}>\n            {steps[currentStep].content}\n          </div>\n\n          <Divider />\n\n          <div style={{ textAlign: 'right' }}>\n            <Space>\n              {currentStep > 0 && (\n                <Button onClick={handlePrev}>\n                  上一步\n                </Button>\n              )}\n              {currentStep < steps.length - 1 && (\n                <Button type=\"primary\" onClick={handleNext}>\n                  下一步\n                </Button>\n              )}\n              {currentStep === steps.length - 1 && (\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<SaveOutlined />}\n                >\n                  创建智能体\n                </Button>\n              )}\n            </Space>\n          </div>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default AgentCreate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,KAAK,QACA,MAAM;AACb,SAASC,iBAAiB,EAAEC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAClF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGrB,UAAU;AACvC,MAAM;EAAEsB;AAAS,CAAC,GAAGnB,KAAK;AAC1B,MAAM;EAAEoB;AAAO,CAAC,GAAGnB,MAAM;AACzB,MAAM;EAAEoB;AAAK,CAAC,GAAGZ,KAAK;AAgBtB,MAAMa,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,CAAC,GAAG1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAMmC,UAAU,GAAG,CACjB;IACEC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,YAAY;IACzBC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEJ,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAW,CAAC,EAC3D;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,eAAe;IAAEC,WAAW,EAAE;EAAW,CAAC,EACrE;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,WAAW,EAAE;EAAU,CAAC,EAChE;IAAEF,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,WAAW,EAAE;EAAS,CAAC,CACpE;EAED,MAAMG,iBAAiB,GAAG,CACxB,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAED,MAAMC,YAAY,GAAG,MAAOC,MAAqB,IAAK;IACpDX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAY,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,MAAM,CAAC;MACtC,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDnC,OAAO,CAACqC,OAAO,CAAC,SAAS,CAAC;MAC1BrB,QAAQ,CAACV,MAAM,CAACgC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,UAAU,GAAGA,CAAA,KAAM;IACvBvB,IAAI,CAACwB,cAAc,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAC/BpB,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACvBrB,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;EACjC,CAAC;EAED,MAAMuB,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,MAAM;IACbC,OAAO,eACLtC,OAAA,CAACZ,GAAG;MAACmD,MAAM,EAAE,EAAG;MAAAC,QAAA,gBACdxC,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAC,MAAM;UACX1B,KAAK,EAAC,gCAAO;UACb2B,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAErD,OAAO,EAAE;UAAW,CAAC,EACvC;YAAEsD,GAAG,EAAE,CAAC;YAAEC,GAAG,EAAE,EAAE;YAAEvD,OAAO,EAAE;UAAkB,CAAC,CAC/C;UAAAgD,QAAA,eAEFxC,OAAA,CAAChB,KAAK;YAACgE,WAAW,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAC,aAAa;UAClB1B,KAAK,EAAC,cAAI;UACV2B,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAErD,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEuD,GAAG,EAAE,GAAG;YAAEvD,OAAO,EAAE;UAAe,CAAC,CACrC;UAAAgD,QAAA,eAEFxC,OAAA,CAACG,QAAQ;YACPkD,IAAI,EAAE,CAAE;YACRL,WAAW,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAC,MAAM;UACX1B,KAAK,EAAC,gCAAO;UACb2B,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErD,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAgD,QAAA,eAEjDxC,OAAA,CAACf,MAAM;YAAC+D,WAAW,EAAC,kDAAU;YAAAR,QAAA,EAC3BzB,UAAU,CAACuC,GAAG,CAACC,IAAI,iBAClBvD,OAAA,CAACI,MAAM;cAAkBY,KAAK,EAAEuC,IAAI,CAACvC,KAAM;cAAAwB,QAAA,eACzCxC,OAAA,CAACb,KAAK;gBAAAqD,QAAA,gBACJxC,OAAA;kBAAAwC,QAAA,EAAOe,IAAI,CAACpC;gBAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBpD,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAAwC,QAAA,EAAMe,IAAI,CAACtC;kBAAK;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvBpD,OAAA;oBAAKwD,KAAK,EAAE;sBAAEC,QAAQ,EAAE,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBAAAlB,QAAA,EACzCe,IAAI,CAACrC;kBAAW;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GATGG,IAAI,CAACvC,KAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,OAAO,eACLtC,OAAA,CAACZ,GAAG;MAACmD,MAAM,EAAE,EAAG;MAAAC,QAAA,gBACdxC,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAC,OAAO;UACZ1B,KAAK,EAAC,0BAAM;UACZ2B,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErD,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAgD,QAAA,eAEhDxC,OAAA,CAACf,MAAM;YAAC+D,WAAW,EAAC,4CAAS;YAAAR,QAAA,EAC1BpB,YAAY,CAACkC,GAAG,CAACK,KAAK,iBACrB3D,OAAA,CAACI,MAAM;cAAmBY,KAAK,EAAE2C,KAAK,CAAC3C,KAAM;cAAAwB,QAAA,eAC3CxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAMmB,KAAK,CAAC1C;gBAAK;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBpD,OAAA;kBAAKwD,KAAK,EAAE;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EACzCmB,KAAK,CAACzC;gBAAW;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GANKO,KAAK,CAAC3C,KAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAC,cAAc;UACnB1B,KAAK,EAAC,0BAAM;UACZ2B,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErD,OAAO,EAAE;UAAY,CAAC,CAAE;UAAAgD,QAAA,eAElDxC,OAAA,CAACf,MAAM;YACL2E,IAAI,EAAC,UAAU;YACfZ,WAAW,EAAC,wDAAW;YACvBQ,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAArB,QAAA,EAExBnB,iBAAiB,CAACiC,GAAG,CAACQ,UAAU,iBAC/B9D,OAAA,CAACI,MAAM;cAAkBY,KAAK,EAAE8C,UAAW;cAAAtB,QAAA,EACxCsB;YAAU,GADAA,UAAU;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,OAAO,eACLtC,OAAA,CAACZ,GAAG;MAACmD,MAAM,EAAE,EAAG;MAAAC,QAAA,gBACdxC,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAE;UAC9B1B,KAAK,EAAC,yBAAU;UAChB8C,OAAO,EAAC,6DAAgB;UAAAvB,QAAA,eAExBxC,OAAA,CAACV,WAAW;YACVwD,GAAG,EAAE,GAAI;YACTC,GAAG,EAAE,IAAK;YACVC,WAAW,EAAC,MAAM;YAClBQ,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAE;UAChC1B,KAAK,EAAC,oBAAK;UACX8C,OAAO,EAAC,uEAAgB;UAAAvB,QAAA,eAExBxC,OAAA,CAACV,WAAW;YACVwD,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPiB,IAAI,EAAE,GAAI;YACVhB,WAAW,EAAC,KAAK;YACjBQ,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAE;UAC5B1B,KAAK,EAAC,kCAAS;UACf8C,OAAO,EAAC,sCAAQ;UAAAvB,QAAA,eAEhBxC,OAAA,CAACV,WAAW;YACVwD,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,GAAI;YACTC,WAAW,EAAC,IAAI;YAChBQ,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACoD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZxC,OAAA,CAACjB,IAAI,CAAC2D,IAAI;UACRC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAE;UAC/B1B,KAAK,EAAC,0BAAM;UACZ8C,OAAO,EAAC,kDAAU;UAAAvB,QAAA,eAElBxC,OAAA,CAACV,WAAW;YACVwD,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,WAAW,EAAC,GAAG;YACfQ,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,CACF;EAED,oBACEpD,OAAA;IAAAwC,QAAA,gBACExC,OAAA;MAAKwD,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAzB,QAAA,eAC/BxC,OAAA,CAACb,KAAK;QAAAqD,QAAA,eACJxC,OAAA,CAACd,MAAM;UACLiC,IAAI,eAAEnB,OAAA,CAACN,iBAAiB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5Bc,OAAO,EAAEA,CAAA,KAAM1D,QAAQ,CAACV,MAAM,CAACgC,MAAM,CAAE;UAAAU,QAAA,EACxC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENpD,OAAA,CAAClB,IAAI;MAAA0D,QAAA,gBACHxC,OAAA;QAAKwD,KAAK,EAAE;UAAES,YAAY,EAAE;QAAG,CAAE;QAAAzB,QAAA,gBAC/BxC,OAAA,CAACC,KAAK;UAACkE,KAAK,EAAE,CAAE;UAACX,KAAK,EAAE;YAAEY,MAAM,EAAE;UAAE,CAAE;UAAA5B,QAAA,gBACpCxC,OAAA,CAACJ,aAAa;YAAC4D,KAAK,EAAE;cAAEa,WAAW,EAAE;YAAE;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kCAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpD,OAAA,CAACE,SAAS;UAACqD,IAAI,EAAC,WAAW;UAAAf,QAAA,EAAC;QAE5B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENpD,OAAA,CAACP,KAAK;QAAC6E,OAAO,EAAEzD,WAAY;QAAC2C,KAAK,EAAE;UAAES,YAAY,EAAE;QAAG,CAAE;QAAAzB,QAAA,EACtDJ,KAAK,CAACkB,GAAG,CAACiB,IAAI,iBACbvE,OAAA,CAACK,IAAI;UAAkBgC,KAAK,EAAEkC,IAAI,CAAClC;QAAM,GAA9BkC,IAAI,CAAClC,KAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAsB,CAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAERpD,OAAA,CAACjB,IAAI;QACH0B,IAAI,EAAEA,IAAK;QACX+D,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEnD,YAAa;QACvBoD,aAAa,EAAE;UACbC,MAAM,EAAE;YACNC,SAAS,EAAE,IAAI;YACfC,WAAW,EAAE,GAAG;YAChBC,OAAO,EAAE,EAAE;YACXC,UAAU,EAAE;UACd;QACF,CAAE;QAAAvC,QAAA,gBAEFxC,OAAA;UAAKwD,KAAK,EAAE;YAAEwB,SAAS,EAAE;UAAI,CAAE;UAAAxC,QAAA,EAC5BJ,KAAK,CAACvB,WAAW,CAAC,CAACyB;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAENpD,OAAA,CAACT,OAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEXpD,OAAA;UAAKwD,KAAK,EAAE;YAAEyB,SAAS,EAAE;UAAQ,CAAE;UAAAzC,QAAA,eACjCxC,OAAA,CAACb,KAAK;YAAAqD,QAAA,GACH3B,WAAW,GAAG,CAAC,iBACdb,OAAA,CAACd,MAAM;cAACgF,OAAO,EAAE/B,UAAW;cAAAK,QAAA,EAAC;YAE7B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EACAvC,WAAW,GAAGuB,KAAK,CAAC8C,MAAM,GAAG,CAAC,iBAC7BlF,OAAA,CAACd,MAAM;cAACqE,IAAI,EAAC,SAAS;cAACW,OAAO,EAAElC,UAAW;cAAAQ,QAAA,EAAC;YAE5C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EACAvC,WAAW,KAAKuB,KAAK,CAAC8C,MAAM,GAAG,CAAC,iBAC/BlF,OAAA,CAACd,MAAM;cACLqE,IAAI,EAAC,SAAS;cACd4B,QAAQ,EAAC,QAAQ;cACjBxE,OAAO,EAAEA,OAAQ;cACjBQ,IAAI,eAAEnB,OAAA,CAACL,YAAY;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EACxB;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAvUID,WAAqB;EAAA,QACRT,WAAW,EACbd,IAAI,CAAC2B,OAAO;AAAA;AAAA0E,EAAA,GAFvB9E,WAAqB;AAyU3B,eAAeA,WAAW;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}