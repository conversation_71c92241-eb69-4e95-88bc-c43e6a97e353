import React, { useEffect, useState } from 'react';
import {
  Typo<PERSON>,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Avatar,
  Tooltip,
  Popconfirm,
  message,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  EyeOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchAgents, deleteAgent, setFilters, setPagination } from '../../store/slices/agentSlice';
import { ROUTES } from '../../utils/constants';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 临时类型定义，直到我们修复slice
interface Agent {
  id: string;
  name: string;
  description: string;
  type: 'chatbot' | 'workflow' | 'recognition' | 'analysis';
  status: 'active' | 'inactive' | 'training' | 'error';
  capabilities: string[];
  model: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  metrics: {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    lastActiveAt: string;
  };
}

const AgentList: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // 使用本地状态暂时替代Redux状态
  const [loading, setLoading] = useState(false);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [filters, setLocalFilters] = useState({
    search: '',
    status: '',
    type: '',
  });
  const [pagination, setLocalPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 模拟数据
  const mockAgents: Agent[] = [
    {
      id: '1',
      name: '智能客服助手',
      description: '处理客户咨询和问题解答',
      type: 'chatbot',
      status: 'active',
      capabilities: ['自然语言理解', '情感分析', '多轮对话'],
      model: 'GPT-4',
      version: '1.2.0',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z',
      metrics: {
        totalRequests: 1234,
        successRate: 0.98,
        averageResponseTime: 850,
        lastActiveAt: '2024-01-20T15:30:00Z',
      },
    },
    {
      id: '2',
      name: '文档处理助手',
      description: '自动处理和分析文档内容',
      type: 'analysis',
      status: 'active',
      capabilities: ['文档解析', '内容提取', '格式转换'],
      model: 'Claude-3',
      version: '2.1.0',
      createdAt: '2024-01-10T09:00:00Z',
      updatedAt: '2024-01-18T14:20:00Z',
      metrics: {
        totalRequests: 856,
        successRate: 0.95,
        averageResponseTime: 1200,
        lastActiveAt: '2024-01-18T14:20:00Z',
      },
    },
    {
      id: '3',
      name: '图像识别助手',
      description: '识别和分析图像内容',
      type: 'recognition',
      status: 'training',
      capabilities: ['图像识别', '物体检测', '场景分析'],
      model: 'Vision-Pro',
      version: '1.0.0',
      createdAt: '2024-01-12T14:00:00Z',
      updatedAt: '2024-01-19T10:15:00Z',
      metrics: {
        totalRequests: 423,
        successRate: 0.92,
        averageResponseTime: 2100,
        lastActiveAt: '2024-01-19T10:15:00Z',
      },
    },
  ];

  useEffect(() => {
    // 模拟加载数据
    setLoading(true);
    setTimeout(() => {
      setAgents(mockAgents);
      setLocalPagination(prev => ({ ...prev, total: mockAgents.length }));
      setLoading(false);
    }, 1000);
  }, []);

  const handleSearch = (value: string) => {
    setLocalFilters(prev => ({ ...prev, search: value }));
    setLocalPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleStatusFilter = (status: string) => {
    setLocalFilters(prev => ({ ...prev, status: status === 'all' ? '' : status }));
    setLocalPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleTypeFilter = (type: string) => {
    setLocalFilters(prev => ({ ...prev, type: type === 'all' ? '' : type }));
    setLocalPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleDelete = async (id: string) => {
    try {
      // 模拟删除
      setAgents(prev => prev.filter(agent => agent.id !== id));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'success',
      inactive: 'default',
      training: 'processing',
      error: 'error',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      chatbot: '💬',
      workflow: '🔄',
      recognition: '👁️',
      analysis: '📊',
    };
    return icons[type as keyof typeof icons] || '🤖';
  };

  const columns: ColumnsType<Agent> = [
    {
      title: '智能体',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Avatar
            size="small"
            style={{ backgroundColor: '#1890ff' }}
            icon={<RobotOutlined />}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <div style={{ fontSize: 12, color: '#666' }}>{record.description}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Space>
          <span>{getTypeIcon(type)}</span>
          <span style={{ textTransform: 'capitalize' }}>{type}</span>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status === 'active' ? '运行中' :
           status === 'inactive' ? '已停止' :
           status === 'training' ? '训练中' : '错误'}
        </Tag>
      ),
    },
    {
      title: '模型',
      dataIndex: 'model',
      key: 'model',
      width: 120,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
    },
    {
      title: '请求数',
      dataIndex: ['metrics', 'totalRequests'],
      key: 'totalRequests',
      width: 100,
      render: (value) => value?.toLocaleString() || 0,
    },
    {
      title: '成功率',
      dataIndex: ['metrics', 'successRate'],
      key: 'successRate',
      width: 100,
      render: (value) => `${((value || 0) * 100).toFixed(1)}%`,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`${ROUTES.AGENTS}/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`${ROUTES.AGENTS}/${record.id}/edit`)}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? '停止' : '启动'}>
            <Button
              type="text"
              size="small"
              icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => {
                // TODO: 实现启动/停止功能
                message.info('功能开发中');
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个智能体吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  // 统计数据
  const stats = {
    total: agents.length,
    active: agents.filter(a => a.status === 'active').length,
    inactive: agents.filter(a => a.status === 'inactive').length,
    training: agents.filter(a => a.status === 'training').length,
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>智能体管理</Title>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>管理和监控您的智能体</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总数"
              value={stats.total}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.active}
              valueStyle={{ color: '#3f8600' }}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已停止"
              value={stats.inactive}
              valueStyle={{ color: '#cf1322' }}
              prefix={<PauseCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="训练中"
              value={stats.training}
              valueStyle={{ color: '#1890ff' }}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space>
              <Search
                placeholder="搜索智能体名称或描述"
                allowClear
                style={{ width: 300 }}
                onSearch={handleSearch}
              />
              <Select
                placeholder="状态筛选"
                style={{ width: 120 }}
                allowClear
                onChange={handleStatusFilter}
              >
                <Option value="all">全部状态</Option>
                <Option value="active">运行中</Option>
                <Option value="inactive">已停止</Option>
                <Option value="training">训练中</Option>
                <Option value="error">错误</Option>
              </Select>
              <Select
                placeholder="类型筛选"
                style={{ width: 120 }}
                allowClear
                onChange={handleTypeFilter}
              >
                <Option value="all">全部类型</Option>
                <Option value="chatbot">聊天机器人</Option>
                <Option value="workflow">工作流</Option>
                <Option value="recognition">识别</Option>
                <Option value="analysis">分析</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate(ROUTES.AGENT_CREATE)}
            >
              创建智能体
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 表格 */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={agents}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setLocalPagination({ current: page, pageSize: pageSize || 10, total: pagination.total });
            },
          }}
        />
      </Card>
    </div>
  );
};

export default AgentList;
