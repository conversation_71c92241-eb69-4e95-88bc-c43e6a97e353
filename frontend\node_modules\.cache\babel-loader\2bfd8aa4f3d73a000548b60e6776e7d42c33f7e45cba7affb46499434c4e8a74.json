{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Recognition\\\\BiometricRecognition.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Typo<PERSON>, Card, Button, Space, Row, Col, Upload, Image, List, Tag, Progress, Alert, Tabs, Select, Switch, Slider, message, Modal, Divider, Statistic, Badge, Steps } from 'antd';\nimport { ArrowLeftOutlined, UserOutlined, UploadOutlined, CameraOutlined, DeleteOutlined, DownloadOutlined, ZoomInOutlined, ClearOutlined, ScanOutlined, EyeOutlined, SafetyCertificateOutlined, FingerprintOutlined, AudioOutlined, CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, PlayCircleOutlined, StopOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst BiometricRecognition = () => {\n  _s();\n  var _biometricTypes$find, _biometricTypes$find2;\n  const navigate = useNavigate();\n  const [fileList, setFileList] = useState([]);\n  const [analyses, setAnalyses] = useState([]);\n  const [selectedAnalysis, setSelectedAnalysis] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [selectedType, setSelectedType] = useState('face');\n  const [confidence, setConfidence] = useState(80);\n  const [enableLiveness, setEnableLiveness] = useState(true);\n  const [enableAntiSpoofing, setEnableAntiSpoofing] = useState(true);\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const cameraRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n\n  // 生物识别类型配置\n  const biometricTypes = [{\n    value: 'face',\n    label: '人脸识别',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this),\n    color: '#1890ff',\n    description: '基于面部特征的身份认证',\n    features: ['活体检测', '防伪检测', '多角度识别', '表情无关']\n  }, {\n    value: 'fingerprint',\n    label: '指纹识别',\n    icon: /*#__PURE__*/_jsxDEV(FingerprintOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this),\n    color: '#52c41a',\n    description: '基于指纹纹路的身份认证',\n    features: ['纹路分析', '特征点提取', '防伪检测', '多指识别']\n  }, {\n    value: 'iris',\n    label: '虹膜识别',\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this),\n    color: '#722ed1',\n    description: '基于虹膜纹理的身份认证',\n    features: ['高精度识别', '活体检测', '远距离识别', '稳定性强']\n  }, {\n    value: 'voice',\n    label: '声纹识别',\n    icon: /*#__PURE__*/_jsxDEV(AudioOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this),\n    color: '#fa8c16',\n    description: '基于声音特征的身份认证',\n    features: ['语音分析', '情感无关', '噪声过滤', '多语言支持']\n  }, {\n    value: 'palm',\n    label: '掌纹识别',\n    icon: /*#__PURE__*/_jsxDEV(SafetyCertificateOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this),\n    color: '#eb2f96',\n    description: '基于掌纹特征的身份认证',\n    features: ['掌纹分析', '静脉识别', '非接触式', '高安全性']\n  }];\n\n  // 模拟生物识别结果\n  const mockResults = {\n    face: [{\n      id: '1',\n      type: 'face',\n      confidence: 0.95,\n      features: {\n        age: '25-35',\n        gender: '女性',\n        emotion: '中性',\n        glasses: false,\n        mask: false,\n        pose: '正面'\n      },\n      timestamp: new Date().toLocaleString(),\n      status: 'success',\n      matchScore: 0.92,\n      liveness: true\n    }],\n    fingerprint: [{\n      id: '2',\n      type: 'fingerprint',\n      confidence: 0.88,\n      features: {\n        pattern: '弓形纹',\n        quality: '优秀',\n        minutiae: 45,\n        ridgeCount: 128\n      },\n      timestamp: new Date().toLocaleString(),\n      status: 'success',\n      matchScore: 0.85\n    }],\n    iris: [{\n      id: '3',\n      type: 'iris',\n      confidence: 0.97,\n      features: {\n        diameter: '11.5mm',\n        texture: '复杂',\n        clarity: '清晰',\n        pupilRatio: 0.35\n      },\n      timestamp: new Date().toLocaleString(),\n      status: 'success',\n      matchScore: 0.94,\n      liveness: true\n    }],\n    voice: [{\n      id: '4',\n      type: 'voice',\n      confidence: 0.82,\n      features: {\n        pitch: '中音',\n        tone: '平稳',\n        accent: '标准普通话',\n        duration: '3.2秒'\n      },\n      timestamp: new Date().toLocaleString(),\n      status: 'success',\n      matchScore: 0.78\n    }],\n    palm: [{\n      id: '5',\n      type: 'palm',\n      confidence: 0.91,\n      features: {\n        palmLines: '清晰',\n        palmPrint: '完整',\n        veinPattern: '可见',\n        handSize: '中等'\n      },\n      timestamp: new Date().toLocaleString(),\n      status: 'success',\n      matchScore: 0.87\n    }]\n  };\n  const handleUpload = async options => {\n    const {\n      file,\n      onSuccess,\n      onError\n    } = options;\n    try {\n      setIsAnalyzing(true);\n\n      // 模拟文件上传和分析\n      await new Promise(resolve => setTimeout(resolve, 3000));\n      const imageUrl = URL.createObjectURL(file);\n      const results = mockResults[selectedType] || [];\n      const newAnalysis = {\n        id: Date.now().toString(),\n        filename: file.name,\n        url: imageUrl,\n        size: file.size,\n        type: selectedType,\n        results: results,\n        timestamp: new Date().toLocaleString(),\n        processingTime: 2.5 + Math.random() * 2,\n        overallScore: 0.85 + Math.random() * 0.15\n      };\n      setAnalyses(prev => [newAnalysis, ...prev]);\n      setSelectedAnalysis(newAnalysis);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(newAnalysis);\n      message.success(`${file.name} 识别完成`);\n    } catch (error) {\n      onError === null || onError === void 0 ? void 0 : onError(error);\n      message.error('识别失败，请重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n  const handlePreview = async file => {\n    if (!file.url && !file.preview) {\n      file.preview = await getBase64(file.originFileObj);\n    }\n    setPreviewImage(file.url || file.preview || '');\n    setPreviewVisible(true);\n  };\n  const getBase64 = file => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result);\n    reader.onerror = error => reject(error);\n  });\n  const startCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: 640,\n          height: 480\n        }\n      });\n      if (cameraRef.current) {\n        cameraRef.current.srcObject = stream;\n        setIsCameraActive(true);\n        message.success('摄像头已启动');\n      }\n    } catch (error) {\n      message.error('无法访问摄像头，请检查权限设置');\n    }\n  };\n  const stopCamera = () => {\n    var _cameraRef$current;\n    if ((_cameraRef$current = cameraRef.current) !== null && _cameraRef$current !== void 0 && _cameraRef$current.srcObject) {\n      const stream = cameraRef.current.srcObject;\n      stream.getTracks().forEach(track => track.stop());\n      cameraRef.current.srcObject = null;\n      setIsCameraActive(false);\n      message.info('摄像头已关闭');\n    }\n  };\n  const startCapture = async () => {\n    setIsCapturing(true);\n    setCurrentStep(0);\n\n    // 模拟采集过程\n    const steps = ['准备采集', '检测中', '特征提取', '质量评估', '完成'];\n    for (let i = 0; i < steps.length; i++) {\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setCurrentStep(i);\n    }\n\n    // 生成模拟结果\n    const results = mockResults[selectedType] || [];\n    const newAnalysis = {\n      id: Date.now().toString(),\n      type: selectedType,\n      results: results,\n      timestamp: new Date().toLocaleString(),\n      processingTime: 5,\n      overallScore: 0.85 + Math.random() * 0.15\n    };\n    setAnalyses(prev => [newAnalysis, ...prev]);\n    setSelectedAnalysis(newAnalysis);\n    setIsCapturing(false);\n    setCurrentStep(0);\n    message.success('生物特征采集完成');\n  };\n  const clearAll = () => {\n    setAnalyses([]);\n    setSelectedAnalysis(null);\n    setFileList([]);\n    message.success('已清空所有识别结果');\n  };\n  const getTypeIcon = type => {\n    const typeConfig = biometricTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.icon) || /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 32\n    }, this);\n  };\n  const getTypeColor = type => {\n    const typeConfig = biometricTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.color) || '#1890ff';\n  };\n  const getTypeLabel = type => {\n    const typeConfig = biometricTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.label) || '未知类型';\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 16\n        }, this);\n      case 'partial':\n        return /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const captureSteps = [{\n    title: '准备采集',\n    description: '初始化采集设备'\n  }, {\n    title: '检测中',\n    description: '检测生物特征'\n  }, {\n    title: '特征提取',\n    description: '提取关键特征'\n  }, {\n    title: '质量评估',\n    description: '评估采集质量'\n  }, {\n    title: '完成',\n    description: '采集完成'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(-1),\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u751F\\u7269\\u8BC6\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            defaultActiveKey: \"upload\",\n            children: [/*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n              tab: \"\\u6587\\u4EF6\\u4E0A\\u4F20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/_jsxDEV(Alert, {\n                  message: \"\\u9009\\u62E9\\u8BC6\\u522B\\u7C7B\\u578B\",\n                  description: \"\\u8BF7\\u5148\\u9009\\u62E9\\u8981\\u8FDB\\u884C\\u7684\\u751F\\u7269\\u8BC6\\u522B\\u7C7B\\u578B\\uFF0C\\u7136\\u540E\\u4E0A\\u4F20\\u76F8\\u5E94\\u7684\\u56FE\\u7247\\u6216\\u97F3\\u9891\\u6587\\u4EF6\\u3002\",\n                  type: \"info\",\n                  showIcon: true,\n                  style: {\n                    marginBottom: 16\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 16\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u8BC6\\u522B\\u7C7B\\u578B\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: selectedType,\n                    onChange: setSelectedType,\n                    style: {\n                      width: 200,\n                      marginLeft: 8\n                    },\n                    children: biometricTypes.map(type => /*#__PURE__*/_jsxDEV(Option, {\n                      value: type.value,\n                      children: /*#__PURE__*/_jsxDEV(Space, {\n                        children: [type.icon, type.label]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 421,\n                        columnNumber: 27\n                      }, this)\n                    }, type.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Upload.Dragger, {\n                name: \"file\",\n                multiple: true,\n                accept: selectedType === 'voice' ? 'audio/*' : 'image/*',\n                customRequest: handleUpload,\n                onPreview: handlePreview,\n                fileList: fileList,\n                onChange: ({\n                  fileList\n                }) => setFileList(fileList),\n                disabled: isAnalyzing,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-drag-icon\",\n                  children: /*#__PURE__*/_jsxDEV(UploadOutlined, {\n                    style: {\n                      fontSize: 48,\n                      color: '#1890ff'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-text\",\n                  children: [\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\", selectedType === 'voice' ? '音频' : '图片', \"\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-hint\",\n                  children: [\"\\u652F\\u6301 \", selectedType === 'voice' ? 'MP3、WAV、AAC' : 'JPG、PNG、BMP', \" \\u7B49\\u683C\\u5F0F\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Progress, {\n                  type: \"circle\",\n                  percent: 75\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u6B63\\u5728\\u8FDB\\u884C\\u751F\\u7269\\u7279\\u5F81\\u8BC6\\u522B\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this)]\n            }, \"upload\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n              tab: \"\\u5B9E\\u65F6\\u91C7\\u96C6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: selectedType === 'voice' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 24\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: 200,\n                        height: 200,\n                        borderRadius: '50%',\n                        background: isCapturing ? 'radial-gradient(circle, rgba(255,77,79,0.3) 0%, rgba(255,77,79,0.1) 70%)' : '#f5f5f5',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        margin: '0 auto',\n                        border: isCapturing ? '3px solid #ff4d4f' : '3px solid #d9d9d9',\n                        transition: 'all 0.3s ease'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(AudioOutlined, {\n                        style: {\n                          fontSize: 64,\n                          color: isCapturing ? '#ff4d4f' : '#999'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 24\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Steps, {\n                      current: currentStep,\n                      size: \"small\",\n                      children: captureSteps.map((step, index) => /*#__PURE__*/_jsxDEV(Steps.Step, {\n                        title: step.title,\n                        description: step.description\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 492,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Space, {\n                    children: !isCapturing ? /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      size: \"large\",\n                      icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 35\n                      }, this),\n                      onClick: startCapture,\n                      children: \"\\u5F00\\u59CB\\u58F0\\u7EB9\\u91C7\\u96C6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"large\",\n                      danger: true,\n                      icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 35\n                      }, this),\n                      onClick: () => setIsCapturing(false),\n                      children: \"\\u505C\\u6B62\\u91C7\\u96C6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                      ref: cameraRef,\n                      autoPlay: true,\n                      playsInline: true,\n                      style: {\n                        width: '100%',\n                        maxWidth: 640,\n                        height: 'auto',\n                        border: '1px solid #d9d9d9',\n                        borderRadius: 6,\n                        display: isCameraActive ? 'block' : 'none'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n                      ref: canvasRef,\n                      style: {\n                        display: 'none'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 25\n                    }, this), !isCameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '100%',\n                        height: 300,\n                        border: '1px dashed #d9d9d9',\n                        borderRadius: 6,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        flexDirection: 'column',\n                        color: '#999'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CameraOutlined, {\n                        style: {\n                          fontSize: 48,\n                          marginBottom: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 550,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: \"\\u70B9\\u51FB\\u4E0B\\u65B9\\u6309\\u94AE\\u542F\\u52A8\\u6444\\u50CF\\u5934\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 551,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 24\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Steps, {\n                      current: currentStep,\n                      size: \"small\",\n                      children: captureSteps.map((step, index) => /*#__PURE__*/_jsxDEV(Steps.Step, {\n                        title: step.title,\n                        description: step.description\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 560,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Space, {\n                    children: !isCameraActive ? /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 570,\n                        columnNumber: 35\n                      }, this),\n                      onClick: startCamera,\n                      children: \"\\u542F\\u52A8\\u6444\\u50CF\\u5934\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 579,\n                          columnNumber: 37\n                        }, this),\n                        onClick: startCapture,\n                        loading: isCapturing,\n                        disabled: isCapturing,\n                        children: \"\\u5F00\\u59CB\\u91C7\\u96C6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 587,\n                          columnNumber: 37\n                        }, this),\n                        onClick: stopCamera,\n                        children: \"\\u5173\\u95ED\\u6444\\u50CF\\u5934\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 586,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)\n            }, \"capture\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BC6\\u522B\\u8BBE\\u7F6E\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u8BC6\\u522B\\u7C7B\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedType,\n                onChange: setSelectedType,\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                children: biometricTypes.map(type => /*#__PURE__*/_jsxDEV(Option, {\n                  value: type.value,\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [type.icon, type.label]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this)\n                }, type.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 8,\n                  fontSize: 12,\n                  color: '#666'\n                },\n                children: (_biometricTypes$find = biometricTypes.find(t => t.value === selectedType)) === null || _biometricTypes$find === void 0 ? void 0 : _biometricTypes$find.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u7F6E\\u4FE1\\u5EA6\\u9608\\u503C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: confidence,\n                onChange: setConfidence,\n                style: {\n                  marginTop: 8\n                },\n                tooltip: {\n                  formatter: value => `${value}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: 12\n                },\n                children: [\"\\u5F53\\u524D: \", confidence, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                style: {\n                  width: '100%',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6D3B\\u4F53\\u68C0\\u6D4B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: enableLiveness,\n                  onChange: setEnableLiveness\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                style: {\n                  width: '100%',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u9632\\u4F2A\\u68C0\\u6D4B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: enableAntiSpoofing,\n                  onChange: setEnableAntiSpoofing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7C7B\\u578B\\u7279\\u6027\",\n          size: \"small\",\n          style: {\n            marginTop: 16\n          },\n          children: (_biometricTypes$find2 = biometricTypes.find(t => t.value === selectedType)) === null || _biometricTypes$find2 === void 0 ? void 0 : _biometricTypes$find2.features.map((feature, index) => /*#__PURE__*/_jsxDEV(Tag, {\n            style: {\n              marginBottom: 8\n            },\n            children: feature\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7EDF\\u8BA1\\u4FE1\\u606F\",\n          size: \"small\",\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5DF2\\u8BC6\\u522B\",\n                value: analyses.length,\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 27\n                }, this),\n                suffix: \"\\u6B21\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5E73\\u5747\\u5F97\\u5206\",\n                value: analyses.length > 0 ? (analyses.reduce((sum, a) => sum + a.overallScore, 0) / analyses.length * 100).toFixed(1) : 0,\n                suffix: \"%\",\n                prefix: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u8BC6\\u522B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n          count: analyses.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 21\n          }, this),\n          onClick: () => message.info('导出功能开发中'),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 21\n          }, this),\n          onClick: clearAll,\n          disabled: analyses.length === 0,\n          children: \"\\u6E05\\u7A7A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 11\n      }, this),\n      style: {\n        marginTop: 24\n      },\n      children: analyses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0',\n          color: '#999'\n        },\n        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {\n          style: {\n            fontSize: 48,\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u6682\\u65E0\\u8BC6\\u522B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12\n          },\n          children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\u6216\\u4F7F\\u7528\\u5B9E\\u65F6\\u91C7\\u96C6\\u6765\\u5F00\\u59CB\\u8BC6\\u522B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: analyses,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                cursor: 'pointer',\n                backgroundColor: (selectedAnalysis === null || selectedAnalysis === void 0 ? void 0 : selectedAnalysis.id) === item.id ? '#f0f0f0' : 'transparent',\n                padding: '8px 12px',\n                borderRadius: 4\n              },\n              onClick: () => setSelectedAnalysis(item),\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: 32,\n                    height: 32,\n                    borderRadius: '50%',\n                    backgroundColor: getTypeColor(item.type),\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white'\n                  },\n                  children: getTypeIcon(item.type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 25\n                }, this),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      fontSize: 12\n                    },\n                    children: item.filename || `${getTypeLabel(item.type)}识别`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: getTypeColor(item.type),\n                    children: getTypeLabel(item.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 25\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 11\n                    },\n                    children: item.timestamp\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 11\n                    },\n                    children: [\"\\u5F97\\u5206: \", (item.overallScore * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 16,\n          children: selectedAnalysis && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [selectedAnalysis.url && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16,\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Image, {\n                src: selectedAnalysis.url,\n                alt: selectedAnalysis.filename,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 300\n                },\n                preview: {\n                  mask: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(ZoomInOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 800,\n                      columnNumber: 31\n                    }, this), \"\\u9884\\u89C8\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 29\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"left\",\n              children: \"\\u8BC6\\u522B\\u8BE6\\u60C5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dataSource: selectedAnalysis.results,\n              renderItem: result => /*#__PURE__*/_jsxDEV(List.Item, {\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getStatusIcon(result.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: getTypeLabel(result.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 819,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                      color: result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red',\n                      children: [\"\\u7F6E\\u4FE1\\u5EA6: \", (result.confidence * 100).toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 820,\n                      columnNumber: 31\n                    }, this), result.matchScore && /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"blue\",\n                      children: [\"\\u5339\\u914D\\u5EA6: \", (result.matchScore * 100).toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 824,\n                      columnNumber: 33\n                    }, this), result.liveness !== undefined && /*#__PURE__*/_jsxDEV(Tag, {\n                      color: result.liveness ? 'green' : 'red',\n                      children: result.liveness ? '活体' : '非活体'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 29\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        style: {\n                          fontSize: 12\n                        },\n                        children: result.timestamp\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: Object.entries(result.features).map(([key, value]) => /*#__PURE__*/_jsxDEV(Tag, {\n                        style: {\n                          marginBottom: 4\n                        },\n                        children: [key, \": \", value]\n                      }, key, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 844,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: previewVisible,\n      title: \"\\u56FE\\u7247\\u9884\\u89C8\",\n      footer: null,\n      onCancel: () => setPreviewVisible(false),\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        alt: \"preview\",\n        style: {\n          width: '100%'\n        },\n        src: previewImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 381,\n    columnNumber: 5\n  }, this);\n};\n_s(BiometricRecognition, \"Vn11GPo5/BKbtODtLalwjFSkpRw=\", false, function () {\n  return [useNavigate];\n});\n_c = BiometricRecognition;\nexport default BiometricRecognition;\nvar _c;\n$RefreshReg$(_c, \"BiometricRecognition\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Typography", "Card", "<PERSON><PERSON>", "Space", "Row", "Col", "Upload", "Image", "List", "Tag", "Progress", "<PERSON><PERSON>", "Tabs", "Select", "Switch", "Slide<PERSON>", "message", "Modal", "Divider", "Statistic", "Badge", "Steps", "ArrowLeftOutlined", "UserOutlined", "UploadOutlined", "CameraOutlined", "DeleteOutlined", "DownloadOutlined", "ZoomInOutlined", "ClearOutlined", "ScanOutlined", "EyeOutlined", "SafetyCertificateOutlined", "FingerprintOutlined", "AudioOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "ExclamationCircleOutlined", "PlayCircleOutlined", "StopOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Paragraph", "Text", "Option", "BiometricRecognition", "_s", "_biometricTypes$find", "_biometricTypes$find2", "navigate", "fileList", "setFileList", "analyses", "setAnalyses", "selectedAnalysis", "setSelectedAnalysis", "isAnalyzing", "setIsAnalyzing", "previewVisible", "setPreviewVisible", "previewImage", "setPreviewImage", "selectedType", "setSelectedType", "confidence", "setConfidence", "enableLiveness", "setEnableLiveness", "enableAntiSpoofing", "setEnableAntiSpoofing", "currentStep", "setCurrentStep", "isCapturing", "setIsCapturing", "cameraRef", "canvasRef", "isCameraActive", "setIsCameraActive", "biometricTypes", "value", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "description", "features", "mockResults", "face", "id", "type", "age", "gender", "emotion", "glasses", "mask", "pose", "timestamp", "Date", "toLocaleString", "status", "matchScore", "liveness", "fingerprint", "pattern", "quality", "minutiae", "ridgeCount", "iris", "diameter", "texture", "clarity", "pupilRatio", "voice", "pitch", "tone", "accent", "duration", "palm", "palmLines", "palmPrint", "veinPattern", "handSize", "handleUpload", "options", "file", "onSuccess", "onError", "Promise", "resolve", "setTimeout", "imageUrl", "URL", "createObjectURL", "results", "newAnalysis", "now", "toString", "filename", "name", "url", "size", "processingTime", "Math", "random", "overallScore", "prev", "success", "error", "handlePreview", "preview", "getBase64", "originFileObj", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "startCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "width", "height", "current", "srcObject", "stopCamera", "_cameraRef$current", "getTracks", "for<PERSON>ach", "track", "stop", "info", "startCapture", "steps", "i", "length", "clearAll", "getTypeIcon", "typeConfig", "find", "t", "getTypeColor", "getTypeLabel", "getStatusIcon", "style", "captureSteps", "title", "children", "marginBottom", "onClick", "gutter", "span", "defaultActiveKey", "TabPane", "tab", "showIcon", "strong", "onChange", "marginLeft", "map", "<PERSON><PERSON>", "multiple", "accept", "customRequest", "onPreview", "disabled", "className", "fontSize", "marginTop", "textAlign", "percent", "borderRadius", "background", "display", "alignItems", "justifyContent", "margin", "border", "transition", "step", "index", "Step", "danger", "ref", "autoPlay", "playsInline", "max<PERSON><PERSON><PERSON>", "flexDirection", "loading", "direction", "tooltip", "formatter", "checked", "feature", "prefix", "suffix", "reduce", "sum", "a", "toFixed", "count", "extra", "padding", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "cursor", "backgroundColor", "Meta", "avatar", "src", "alt", "maxHeight", "orientation", "undefined", "Object", "entries", "key", "open", "footer", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Recognition/BiometricRecognition.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  <PERSON>po<PERSON>,\n  <PERSON>,\n  Button,\n  Space,\n  Row,\n  Col,\n  Upload,\n  Image,\n  List,\n  Tag,\n  Progress,\n  Alert,\n  Tabs,\n  Select,\n  Switch,\n  Slider,\n  message,\n  Modal,\n  Divider,\n  Statistic,\n  Badge,\n  Steps,\n  Result,\n  Spin,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  UserOutlined,\n  UploadOutlined,\n  CameraOutlined,\n  DeleteOutlined,\n  DownloadOutlined,\n  ZoomInOutlined,\n  ClearOutlined,\n  ScanOutlined,\n  EyeOutlined,\n  SafetyCertificateOutlined,\n  FingerprintOutlined,\n  AudioOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  ExclamationCircleOutlined,\n  PlayCircleOutlined,\n  StopOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport type { UploadFile, UploadProps } from 'antd/es/upload/interface';\n\nconst { Title, Paragraph, Text } = Typography;\nconst { Option } = Select;\n\ninterface BiometricResult {\n  id: string;\n  type: 'face' | 'fingerprint' | 'iris' | 'voice' | 'palm';\n  confidence: number;\n  features: Record<string, any>;\n  timestamp: string;\n  status: 'success' | 'failed' | 'partial';\n  matchScore?: number;\n  liveness?: boolean;\n}\n\ninterface BiometricAnalysis {\n  id: string;\n  filename?: string;\n  url?: string;\n  size?: number;\n  type: 'face' | 'fingerprint' | 'iris' | 'voice' | 'palm';\n  results: BiometricResult[];\n  timestamp: string;\n  processingTime: number;\n  overallScore: number;\n}\n\nconst BiometricRecognition: React.FC = () => {\n  const navigate = useNavigate();\n  const [fileList, setFileList] = useState<UploadFile[]>([]);\n  const [analyses, setAnalyses] = useState<BiometricAnalysis[]>([]);\n  const [selectedAnalysis, setSelectedAnalysis] = useState<BiometricAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [selectedType, setSelectedType] = useState<string>('face');\n  const [confidence, setConfidence] = useState(80);\n  const [enableLiveness, setEnableLiveness] = useState(true);\n  const [enableAntiSpoofing, setEnableAntiSpoofing] = useState(true);\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isCapturing, setIsCapturing] = useState(false);\n\n  const cameraRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n\n  // 生物识别类型配置\n  const biometricTypes = [\n    {\n      value: 'face',\n      label: '人脸识别',\n      icon: <UserOutlined />,\n      color: '#1890ff',\n      description: '基于面部特征的身份认证',\n      features: ['活体检测', '防伪检测', '多角度识别', '表情无关']\n    },\n    {\n      value: 'fingerprint',\n      label: '指纹识别',\n      icon: <FingerprintOutlined />,\n      color: '#52c41a',\n      description: '基于指纹纹路的身份认证',\n      features: ['纹路分析', '特征点提取', '防伪检测', '多指识别']\n    },\n    {\n      value: 'iris',\n      label: '虹膜识别',\n      icon: <EyeOutlined />,\n      color: '#722ed1',\n      description: '基于虹膜纹理的身份认证',\n      features: ['高精度识别', '活体检测', '远距离识别', '稳定性强']\n    },\n    {\n      value: 'voice',\n      label: '声纹识别',\n      icon: <AudioOutlined />,\n      color: '#fa8c16',\n      description: '基于声音特征的身份认证',\n      features: ['语音分析', '情感无关', '噪声过滤', '多语言支持']\n    },\n    {\n      value: 'palm',\n      label: '掌纹识别',\n      icon: <SafetyCertificateOutlined />,\n      color: '#eb2f96',\n      description: '基于掌纹特征的身份认证',\n      features: ['掌纹分析', '静脉识别', '非接触式', '高安全性']\n    },\n  ];\n\n  // 模拟生物识别结果\n  const mockResults: Record<string, BiometricResult[]> = {\n    face: [\n      {\n        id: '1',\n        type: 'face',\n        confidence: 0.95,\n        features: {\n          age: '25-35',\n          gender: '女性',\n          emotion: '中性',\n          glasses: false,\n          mask: false,\n          pose: '正面',\n        },\n        timestamp: new Date().toLocaleString(),\n        status: 'success',\n        matchScore: 0.92,\n        liveness: true,\n      }\n    ],\n    fingerprint: [\n      {\n        id: '2',\n        type: 'fingerprint',\n        confidence: 0.88,\n        features: {\n          pattern: '弓形纹',\n          quality: '优秀',\n          minutiae: 45,\n          ridgeCount: 128,\n        },\n        timestamp: new Date().toLocaleString(),\n        status: 'success',\n        matchScore: 0.85,\n      }\n    ],\n    iris: [\n      {\n        id: '3',\n        type: 'iris',\n        confidence: 0.97,\n        features: {\n          diameter: '11.5mm',\n          texture: '复杂',\n          clarity: '清晰',\n          pupilRatio: 0.35,\n        },\n        timestamp: new Date().toLocaleString(),\n        status: 'success',\n        matchScore: 0.94,\n        liveness: true,\n      }\n    ],\n    voice: [\n      {\n        id: '4',\n        type: 'voice',\n        confidence: 0.82,\n        features: {\n          pitch: '中音',\n          tone: '平稳',\n          accent: '标准普通话',\n          duration: '3.2秒',\n        },\n        timestamp: new Date().toLocaleString(),\n        status: 'success',\n        matchScore: 0.78,\n      }\n    ],\n    palm: [\n      {\n        id: '5',\n        type: 'palm',\n        confidence: 0.91,\n        features: {\n          palmLines: '清晰',\n          palmPrint: '完整',\n          veinPattern: '可见',\n          handSize: '中等',\n        },\n        timestamp: new Date().toLocaleString(),\n        status: 'success',\n        matchScore: 0.87,\n      }\n    ],\n  };\n\n  const handleUpload: UploadProps['customRequest'] = async (options) => {\n    const { file, onSuccess, onError } = options;\n\n    try {\n      setIsAnalyzing(true);\n\n      // 模拟文件上传和分析\n      await new Promise(resolve => setTimeout(resolve, 3000));\n\n      const imageUrl = URL.createObjectURL(file as File);\n      const results = mockResults[selectedType] || [];\n\n      const newAnalysis: BiometricAnalysis = {\n        id: Date.now().toString(),\n        filename: (file as File).name,\n        url: imageUrl,\n        size: (file as File).size,\n        type: selectedType as any,\n        results: results,\n        timestamp: new Date().toLocaleString(),\n        processingTime: 2.5 + Math.random() * 2,\n        overallScore: 0.85 + Math.random() * 0.15,\n      };\n\n      setAnalyses(prev => [newAnalysis, ...prev]);\n      setSelectedAnalysis(newAnalysis);\n\n      onSuccess?.(newAnalysis);\n      message.success(`${(file as File).name} 识别完成`);\n    } catch (error) {\n      onError?.(error as Error);\n      message.error('识别失败，请重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const handlePreview = async (file: UploadFile) => {\n    if (!file.url && !file.preview) {\n      file.preview = await getBase64(file.originFileObj as File);\n    }\n    setPreviewImage(file.url || file.preview || '');\n    setPreviewVisible(true);\n  };\n\n  const getBase64 = (file: File): Promise<string> =>\n    new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result as string);\n      reader.onerror = error => reject(error);\n    });\n\n  const startCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { width: 640, height: 480 }\n      });\n      if (cameraRef.current) {\n        cameraRef.current.srcObject = stream;\n        setIsCameraActive(true);\n        message.success('摄像头已启动');\n      }\n    } catch (error) {\n      message.error('无法访问摄像头，请检查权限设置');\n    }\n  };\n\n  const stopCamera = () => {\n    if (cameraRef.current?.srcObject) {\n      const stream = cameraRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      cameraRef.current.srcObject = null;\n      setIsCameraActive(false);\n      message.info('摄像头已关闭');\n    }\n  };\n\n  const startCapture = async () => {\n    setIsCapturing(true);\n    setCurrentStep(0);\n\n    // 模拟采集过程\n    const steps = ['准备采集', '检测中', '特征提取', '质量评估', '完成'];\n\n    for (let i = 0; i < steps.length; i++) {\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setCurrentStep(i);\n    }\n\n    // 生成模拟结果\n    const results = mockResults[selectedType] || [];\n    const newAnalysis: BiometricAnalysis = {\n      id: Date.now().toString(),\n      type: selectedType as any,\n      results: results,\n      timestamp: new Date().toLocaleString(),\n      processingTime: 5,\n      overallScore: 0.85 + Math.random() * 0.15,\n    };\n\n    setAnalyses(prev => [newAnalysis, ...prev]);\n    setSelectedAnalysis(newAnalysis);\n    setIsCapturing(false);\n    setCurrentStep(0);\n\n    message.success('生物特征采集完成');\n  };\n\n  const clearAll = () => {\n    setAnalyses([]);\n    setSelectedAnalysis(null);\n    setFileList([]);\n    message.success('已清空所有识别结果');\n  };\n\n  const getTypeIcon = (type: string) => {\n    const typeConfig = biometricTypes.find(t => t.value === type);\n    return typeConfig?.icon || <UserOutlined />;\n  };\n\n  const getTypeColor = (type: string) => {\n    const typeConfig = biometricTypes.find(t => t.value === type);\n    return typeConfig?.color || '#1890ff';\n  };\n\n  const getTypeLabel = (type: string) => {\n    const typeConfig = biometricTypes.find(t => t.value === type);\n    return typeConfig?.label || '未知类型';\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'success':\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'failed':\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case 'partial':\n        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ScanOutlined />;\n    }\n  };\n\n  const captureSteps = [\n    { title: '准备采集', description: '初始化采集设备' },\n    { title: '检测中', description: '检测生物特征' },\n    { title: '特征提取', description: '提取关键特征' },\n    { title: '质量评估', description: '评估采集质量' },\n    { title: '完成', description: '采集完成' },\n  ];\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(-1)}\n          >\n            返回\n          </Button>\n        </Space>\n      </div>\n\n      <Row gutter={24}>\n        <Col span={16}>\n          <Card title={\n            <Space>\n              <UserOutlined />\n              <span>生物识别</span>\n            </Space>\n          }>\n            <Tabs defaultActiveKey=\"upload\">\n              <Tabs.TabPane tab=\"文件上传\" key=\"upload\">\n                <div style={{ marginBottom: 16 }}>\n                  <Alert\n                    message=\"选择识别类型\"\n                    description=\"请先选择要进行的生物识别类型，然后上传相应的图片或音频文件。\"\n                    type=\"info\"\n                    showIcon\n                    style={{ marginBottom: 16 }}\n                  />\n\n                  <div style={{ marginBottom: 16 }}>\n                    <Text strong>识别类型：</Text>\n                    <Select\n                      value={selectedType}\n                      onChange={setSelectedType}\n                      style={{ width: 200, marginLeft: 8 }}\n                    >\n                      {biometricTypes.map(type => (\n                        <Option key={type.value} value={type.value}>\n                          <Space>\n                            {type.icon}\n                            {type.label}\n                          </Space>\n                        </Option>\n                      ))}\n                    </Select>\n                  </div>\n                </div>\n\n                <Upload.Dragger\n                  name=\"file\"\n                  multiple\n                  accept={selectedType === 'voice' ? 'audio/*' : 'image/*'}\n                  customRequest={handleUpload}\n                  onPreview={handlePreview}\n                  fileList={fileList}\n                  onChange={({ fileList }) => setFileList(fileList)}\n                  disabled={isAnalyzing}\n                >\n                  <p className=\"ant-upload-drag-icon\">\n                    <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n                  </p>\n                  <p className=\"ant-upload-text\">\n                    点击或拖拽{selectedType === 'voice' ? '音频' : '图片'}文件到此区域上传\n                  </p>\n                  <p className=\"ant-upload-hint\">\n                    支持 {selectedType === 'voice' ? 'MP3、WAV、AAC' : 'JPG、PNG、BMP'} 等格式\n                  </p>\n                </Upload.Dragger>\n\n                {isAnalyzing && (\n                  <div style={{ marginTop: 16, textAlign: 'center' }}>\n                    <Progress type=\"circle\" percent={75} />\n                    <div style={{ marginTop: 8 }}>\n                      <Text>正在进行生物特征识别，请稍候...</Text>\n                    </div>\n                  </div>\n                )}\n              </Tabs.TabPane>\n\n              <Tabs.TabPane tab=\"实时采集\" key=\"capture\">\n                <div style={{ textAlign: 'center' }}>\n                  {selectedType === 'voice' ? (\n                    <div>\n                      <div style={{ marginBottom: 24 }}>\n                        <div style={{\n                          width: 200,\n                          height: 200,\n                          borderRadius: '50%',\n                          background: isCapturing ?\n                            'radial-gradient(circle, rgba(255,77,79,0.3) 0%, rgba(255,77,79,0.1) 70%)' :\n                            '#f5f5f5',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          margin: '0 auto',\n                          border: isCapturing ? '3px solid #ff4d4f' : '3px solid #d9d9d9',\n                          transition: 'all 0.3s ease',\n                        }}>\n                          <AudioOutlined style={{\n                            fontSize: 64,\n                            color: isCapturing ? '#ff4d4f' : '#999',\n                          }} />\n                        </div>\n                      </div>\n\n                      {isCapturing && (\n                        <div style={{ marginBottom: 24 }}>\n                          <Steps current={currentStep} size=\"small\">\n                            {captureSteps.map((step, index) => (\n                              <Steps.Step key={index} title={step.title} description={step.description} />\n                            ))}\n                          </Steps>\n                        </div>\n                      )}\n\n                      <Space>\n                        {!isCapturing ? (\n                          <Button\n                            type=\"primary\"\n                            size=\"large\"\n                            icon={<PlayCircleOutlined />}\n                            onClick={startCapture}\n                          >\n                            开始声纹采集\n                          </Button>\n                        ) : (\n                          <Button\n                            size=\"large\"\n                            danger\n                            icon={<StopOutlined />}\n                            onClick={() => setIsCapturing(false)}\n                          >\n                            停止采集\n                          </Button>\n                        )}\n                      </Space>\n                    </div>\n                  ) : (\n                    <div>\n                      <div style={{ marginBottom: 16 }}>\n                        <video\n                          ref={cameraRef}\n                          autoPlay\n                          playsInline\n                          style={{\n                            width: '100%',\n                            maxWidth: 640,\n                            height: 'auto',\n                            border: '1px solid #d9d9d9',\n                            borderRadius: 6,\n                            display: isCameraActive ? 'block' : 'none',\n                          }}\n                        />\n                        <canvas ref={canvasRef} style={{ display: 'none' }} />\n\n                        {!isCameraActive && (\n                          <div style={{\n                            width: '100%',\n                            height: 300,\n                            border: '1px dashed #d9d9d9',\n                            borderRadius: 6,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            flexDirection: 'column',\n                            color: '#999',\n                          }}>\n                            <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />\n                            <Text type=\"secondary\">点击下方按钮启动摄像头</Text>\n                          </div>\n                        )}\n                      </div>\n\n                      {isCapturing && (\n                        <div style={{ marginBottom: 24 }}>\n                          <Steps current={currentStep} size=\"small\">\n                            {captureSteps.map((step, index) => (\n                              <Steps.Step key={index} title={step.title} description={step.description} />\n                            ))}\n                          </Steps>\n                        </div>\n                      )}\n\n                      <Space>\n                        {!isCameraActive ? (\n                          <Button\n                            type=\"primary\"\n                            icon={<CameraOutlined />}\n                            onClick={startCamera}\n                          >\n                            启动摄像头\n                          </Button>\n                        ) : (\n                          <>\n                            <Button\n                              type=\"primary\"\n                              icon={<ScanOutlined />}\n                              onClick={startCapture}\n                              loading={isCapturing}\n                              disabled={isCapturing}\n                            >\n                              开始采集\n                            </Button>\n                            <Button\n                              icon={<DeleteOutlined />}\n                              onClick={stopCamera}\n                            >\n                              关闭摄像头\n                            </Button>\n                          </>\n                        )}\n                      </Space>\n                    </div>\n                  )}\n                </div>\n              </Tabs.TabPane>\n            </Tabs>\n          </Card>\n        </Col>\n\n        <Col span={8}>\n          <Card title=\"识别设置\" size=\"small\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>识别类型</Text>\n                <Select\n                  value={selectedType}\n                  onChange={setSelectedType}\n                  style={{ width: '100%', marginTop: 8 }}\n                >\n                  {biometricTypes.map(type => (\n                    <Option key={type.value} value={type.value}>\n                      <Space>\n                        {type.icon}\n                        {type.label}\n                      </Space>\n                    </Option>\n                  ))}\n                </Select>\n                <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>\n                  {biometricTypes.find(t => t.value === selectedType)?.description}\n                </div>\n              </div>\n\n              <div>\n                <Text strong>置信度阈值</Text>\n                <Slider\n                  value={confidence}\n                  onChange={setConfidence}\n                  style={{ marginTop: 8 }}\n                  tooltip={{ formatter: (value) => `${value}%` }}\n                />\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                  当前: {confidence}%\n                </Text>\n              </div>\n\n              <div>\n                <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n                  <Text strong>活体检测</Text>\n                  <Switch\n                    checked={enableLiveness}\n                    onChange={setEnableLiveness}\n                  />\n                </Space>\n              </div>\n\n              <div>\n                <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n                  <Text strong>防伪检测</Text>\n                  <Switch\n                    checked={enableAntiSpoofing}\n                    onChange={setEnableAntiSpoofing}\n                  />\n                </Space>\n              </div>\n            </Space>\n          </Card>\n\n          <Card title=\"类型特性\" size=\"small\" style={{ marginTop: 16 }}>\n            {biometricTypes.find(t => t.value === selectedType)?.features.map((feature, index) => (\n              <Tag key={index} style={{ marginBottom: 8 }}>\n                {feature}\n              </Tag>\n            ))}\n          </Card>\n\n          <Card title=\"统计信息\" size=\"small\" style={{ marginTop: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic\n                  title=\"已识别\"\n                  value={analyses.length}\n                  prefix={<UserOutlined />}\n                  suffix=\"次\"\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"平均得分\"\n                  value={analyses.length > 0 ?\n                    (analyses.reduce((sum, a) => sum + a.overallScore, 0) / analyses.length * 100).toFixed(1) :\n                    0\n                  }\n                  suffix=\"%\"\n                  prefix={<ScanOutlined />}\n                />\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 识别结果 */}\n      <Card\n        title={\n          <Space>\n            <UserOutlined />\n            <span>识别结果</span>\n            <Badge count={analyses.length} />\n          </Space>\n        }\n        extra={\n          <Space>\n            <Button\n              icon={<DownloadOutlined />}\n              onClick={() => message.info('导出功能开发中')}\n            >\n              导出\n            </Button>\n            <Button\n              icon={<ClearOutlined />}\n              onClick={clearAll}\n              disabled={analyses.length === 0}\n            >\n              清空\n            </Button>\n          </Space>\n        }\n        style={{ marginTop: 24 }}\n      >\n        {analyses.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>\n            <UserOutlined style={{ fontSize: 48, marginBottom: 16 }} />\n            <div>暂无识别结果</div>\n            <div style={{ fontSize: 12 }}>上传文件或使用实时采集来开始识别</div>\n          </div>\n        ) : (\n          <Row gutter={16}>\n            <Col span={8}>\n              <List\n                size=\"small\"\n                dataSource={analyses}\n                renderItem={(item) => (\n                  <List.Item\n                    style={{\n                      cursor: 'pointer',\n                      backgroundColor: selectedAnalysis?.id === item.id ? '#f0f0f0' : 'transparent',\n                      padding: '8px 12px',\n                      borderRadius: 4,\n                    }}\n                    onClick={() => setSelectedAnalysis(item)}\n                  >\n                    <List.Item.Meta\n                      avatar={\n                        <div style={{\n                          width: 32,\n                          height: 32,\n                          borderRadius: '50%',\n                          backgroundColor: getTypeColor(item.type),\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: 'white',\n                        }}>\n                          {getTypeIcon(item.type)}\n                        </div>\n                      }\n                      title={\n                        <Space>\n                          <Text strong style={{ fontSize: 12 }}>\n                            {item.filename || `${getTypeLabel(item.type)}识别`}\n                          </Text>\n                          <Tag color={getTypeColor(item.type)}>\n                            {getTypeLabel(item.type)}\n                          </Tag>\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                            {item.timestamp}\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                            得分: {(item.overallScore * 100).toFixed(1)}%\n                          </Text>\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Col>\n\n            <Col span={16}>\n              {selectedAnalysis && (\n                <div>\n                  {selectedAnalysis.url && (\n                    <div style={{ marginBottom: 16, textAlign: 'center' }}>\n                      <Image\n                        src={selectedAnalysis.url}\n                        alt={selectedAnalysis.filename}\n                        style={{ maxWidth: '100%', maxHeight: 300 }}\n                        preview={{\n                          mask: (\n                            <Space>\n                              <ZoomInOutlined />\n                              预览\n                            </Space>\n                          ),\n                        }}\n                      />\n                    </div>\n                  )}\n\n                  <Divider orientation=\"left\">识别详情</Divider>\n\n                  <List\n                    dataSource={selectedAnalysis.results}\n                    renderItem={(result) => (\n                      <List.Item>\n                        <List.Item.Meta\n                          avatar={getStatusIcon(result.status)}\n                          title={\n                            <Space>\n                              <Text strong>{getTypeLabel(result.type)}</Text>\n                              <Tag color={result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red'}>\n                                置信度: {(result.confidence * 100).toFixed(1)}%\n                              </Tag>\n                              {result.matchScore && (\n                                <Tag color=\"blue\">\n                                  匹配度: {(result.matchScore * 100).toFixed(1)}%\n                                </Tag>\n                              )}\n                              {result.liveness !== undefined && (\n                                <Tag color={result.liveness ? 'green' : 'red'}>\n                                  {result.liveness ? '活体' : '非活体'}\n                                </Tag>\n                              )}\n                            </Space>\n                          }\n                          description={\n                            <div>\n                              <div style={{ marginBottom: 8 }}>\n                                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                                  {result.timestamp}\n                                </Text>\n                              </div>\n                              <div>\n                                {Object.entries(result.features).map(([key, value]) => (\n                                  <Tag key={key} style={{ marginBottom: 4 }}>\n                                    {key}: {value}\n                                  </Tag>\n                                ))}\n                              </div>\n                            </div>\n                          }\n                        />\n                      </List.Item>\n                    )}\n                  />\n                </div>\n              )}\n            </Col>\n          </Row>\n        )}\n      </Card>\n\n      {/* 预览模态框 */}\n      <Modal\n        open={previewVisible}\n        title=\"图片预览\"\n        footer={null}\n        onCancel={() => setPreviewVisible(false)}\n        width={800}\n      >\n        <img alt=\"preview\" style={{ width: '100%' }} src={previewImage} />\n      </Modal>\n    </div>\n  );\n};\n\nexport default BiometricRecognition;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D,SACEC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,KAAK,QAGA,MAAM;AACb,SACEC,iBAAiB,EACjBC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,WAAW,EACXC,yBAAyB,EACzBC,mBAAmB,EACnBC,aAAa,EACbC,mBAAmB,EACnBC,mBAAmB,EACnBC,yBAAyB,EACzBC,kBAAkB,EAClBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG/C,MAAM;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAG/C,UAAU;AAC7C,MAAM;EAAEgD;AAAO,CAAC,GAAGnC,MAAM;AAyBzB,MAAMoC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EAC3C,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAsB,EAAE,CAAC;EACjE,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAA2B,IAAI,CAAC;EACxF,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgE,cAAc,EAAEC,iBAAiB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAS,MAAM,CAAC;EAChE,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMgF,SAAS,GAAG/E,MAAM,CAAmB,IAAI,CAAC;EAChD,MAAMgF,SAAS,GAAGhF,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAM,CAACiF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMoF,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAE3C,OAAA,CAACnB,YAAY;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;EAC5C,CAAC,EACD;IACET,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAE3C,OAAA,CAACT,mBAAmB;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;EAC5C,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAE3C,OAAA,CAACX,WAAW;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;EAC7C,CAAC,EACD;IACET,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAE3C,OAAA,CAACR,aAAa;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;EAC5C,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAE3C,OAAA,CAACV,yBAAyB;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnCC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;EAC3C,CAAC,CACF;;EAED;EACA,MAAMC,WAA8C,GAAG;IACrDC,IAAI,EAAE,CACJ;MACEC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,MAAM;MACZ5B,UAAU,EAAE,IAAI;MAChBwB,QAAQ,EAAE;QACRK,GAAG,EAAE,OAAO;QACZC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAE;MACR,CAAC;MACDC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtCC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDC,WAAW,EAAE,CACX;MACEd,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,aAAa;MACnB5B,UAAU,EAAE,IAAI;MAChBwB,QAAQ,EAAE;QACRkB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;MACd,CAAC;MACDV,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtCC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC,CACF;IACDO,IAAI,EAAE,CACJ;MACEnB,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,MAAM;MACZ5B,UAAU,EAAE,IAAI;MAChBwB,QAAQ,EAAE;QACRuB,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE;MACd,CAAC;MACDf,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtCC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDW,KAAK,EAAE,CACL;MACExB,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,OAAO;MACb5B,UAAU,EAAE,IAAI;MAChBwB,QAAQ,EAAE;QACR4B,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAC;MACDpB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtCC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC,CACF;IACDiB,IAAI,EAAE,CACJ;MACE7B,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,MAAM;MACZ5B,UAAU,EAAE,IAAI;MAChBwB,QAAQ,EAAE;QACRiC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE;MACZ,CAAC;MACDzB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtCC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC;EAED,MAAMsB,YAA0C,GAAG,MAAOC,OAAO,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAGH,OAAO;IAE5C,IAAI;MACFrE,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM,IAAIyE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAY,CAAC;MAClD,MAAMS,OAAO,GAAG/C,WAAW,CAAC3B,YAAY,CAAC,IAAI,EAAE;MAE/C,MAAM2E,WAA8B,GAAG;QACrC9C,EAAE,EAAES,IAAI,CAACsC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,QAAQ,EAAGb,IAAI,CAAUc,IAAI;QAC7BC,GAAG,EAAET,QAAQ;QACbU,IAAI,EAAGhB,IAAI,CAAUgB,IAAI;QACzBnD,IAAI,EAAE9B,YAAmB;QACzB0E,OAAO,EAAEA,OAAO;QAChBrC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QACtC2C,cAAc,EAAE,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QACvCC,YAAY,EAAE,IAAI,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MACvC,CAAC;MAED7F,WAAW,CAAC+F,IAAI,IAAI,CAACX,WAAW,EAAE,GAAGW,IAAI,CAAC,CAAC;MAC3C7F,mBAAmB,CAACkF,WAAW,CAAC;MAEhCT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGS,WAAW,CAAC;MACxB7H,OAAO,CAACyI,OAAO,CAAC,GAAItB,IAAI,CAAUc,IAAI,OAAO,CAAC;IAChD,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdrB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGqB,KAAc,CAAC;MACzB1I,OAAO,CAAC0I,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR7F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM8F,aAAa,GAAG,MAAOxB,IAAgB,IAAK;IAChD,IAAI,CAACA,IAAI,CAACe,GAAG,IAAI,CAACf,IAAI,CAACyB,OAAO,EAAE;MAC9BzB,IAAI,CAACyB,OAAO,GAAG,MAAMC,SAAS,CAAC1B,IAAI,CAAC2B,aAAqB,CAAC;IAC5D;IACA7F,eAAe,CAACkE,IAAI,CAACe,GAAG,IAAIf,IAAI,CAACyB,OAAO,IAAI,EAAE,CAAC;IAC/C7F,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM8F,SAAS,GAAI1B,IAAU,IAC3B,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEwB,MAAM,KAAK;IAC/B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,aAAa,CAAC/B,IAAI,CAAC;IAC1B6B,MAAM,CAACG,MAAM,GAAG,MAAM5B,OAAO,CAACyB,MAAM,CAACI,MAAgB,CAAC;IACtDJ,MAAM,CAACK,OAAO,GAAGX,KAAK,IAAIK,MAAM,CAACL,KAAK,CAAC;EACzC,CAAC,CAAC;EAEJ,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI;MACnC,CAAC,CAAC;MACF,IAAI/F,SAAS,CAACgG,OAAO,EAAE;QACrBhG,SAAS,CAACgG,OAAO,CAACC,SAAS,GAAGR,MAAM;QACpCtF,iBAAiB,CAAC,IAAI,CAAC;QACvBjE,OAAO,CAACyI,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1I,OAAO,CAAC0I,KAAK,CAAC,iBAAiB,CAAC;IAClC;EACF,CAAC;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA;IACvB,KAAAA,kBAAA,GAAInG,SAAS,CAACgG,OAAO,cAAAG,kBAAA,eAAjBA,kBAAA,CAAmBF,SAAS,EAAE;MAChC,MAAMR,MAAM,GAAGzF,SAAS,CAACgG,OAAO,CAACC,SAAwB;MACzDR,MAAM,CAACW,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACjDvG,SAAS,CAACgG,OAAO,CAACC,SAAS,GAAG,IAAI;MAClC9F,iBAAiB,CAAC,KAAK,CAAC;MACxBjE,OAAO,CAACsK,IAAI,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B1G,cAAc,CAAC,IAAI,CAAC;IACpBF,cAAc,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAM6G,KAAK,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IAEnD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAM,IAAInD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvD5D,cAAc,CAAC8G,CAAC,CAAC;IACnB;;IAEA;IACA,MAAM7C,OAAO,GAAG/C,WAAW,CAAC3B,YAAY,CAAC,IAAI,EAAE;IAC/C,MAAM2E,WAA8B,GAAG;MACrC9C,EAAE,EAAES,IAAI,CAACsC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzB/C,IAAI,EAAE9B,YAAmB;MACzB0E,OAAO,EAAEA,OAAO;MAChBrC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtC2C,cAAc,EAAE,CAAC;MACjBG,YAAY,EAAE,IAAI,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;IACvC,CAAC;IAED7F,WAAW,CAAC+F,IAAI,IAAI,CAACX,WAAW,EAAE,GAAGW,IAAI,CAAC,CAAC;IAC3C7F,mBAAmB,CAACkF,WAAW,CAAC;IAChChE,cAAc,CAAC,KAAK,CAAC;IACrBF,cAAc,CAAC,CAAC,CAAC;IAEjB3D,OAAO,CAACyI,OAAO,CAAC,UAAU,CAAC;EAC7B,CAAC;EAED,MAAMkC,QAAQ,GAAGA,CAAA,KAAM;IACrBlI,WAAW,CAAC,EAAE,CAAC;IACfE,mBAAmB,CAAC,IAAI,CAAC;IACzBJ,WAAW,CAAC,EAAE,CAAC;IACfvC,OAAO,CAACyI,OAAO,CAAC,WAAW,CAAC;EAC9B,CAAC;EAED,MAAMmC,WAAW,GAAI5F,IAAY,IAAK;IACpC,MAAM6F,UAAU,GAAG3G,cAAc,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,KAAK,KAAKa,IAAI,CAAC;IAC7D,OAAO,CAAA6F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAExG,IAAI,kBAAI3C,OAAA,CAACnB,YAAY;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C,CAAC;EAED,MAAMuG,YAAY,GAAIhG,IAAY,IAAK;IACrC,MAAM6F,UAAU,GAAG3G,cAAc,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,KAAK,KAAKa,IAAI,CAAC;IAC7D,OAAO,CAAA6F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEnG,KAAK,KAAI,SAAS;EACvC,CAAC;EAED,MAAMuG,YAAY,GAAIjG,IAAY,IAAK;IACrC,MAAM6F,UAAU,GAAG3G,cAAc,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,KAAK,KAAKa,IAAI,CAAC;IAC7D,OAAO,CAAA6F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzG,KAAK,KAAI,MAAM;EACpC,CAAC;EAED,MAAM8G,aAAa,GAAIxF,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOhE,OAAA,CAACP,mBAAmB;UAACgK,KAAK,EAAE;YAAEzG,KAAK,EAAE;UAAU;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,QAAQ;QACX,oBAAO/C,OAAA,CAACN,mBAAmB;UAAC+J,KAAK,EAAE;YAAEzG,KAAK,EAAE;UAAU;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,SAAS;QACZ,oBAAO/C,OAAA,CAACL,yBAAyB;UAAC8J,KAAK,EAAE;YAAEzG,KAAK,EAAE;UAAU;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE;QACE,oBAAO/C,OAAA,CAACZ,YAAY;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3B;EACF,CAAC;EAED,MAAM2G,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,MAAM;IAAE1G,WAAW,EAAE;EAAU,CAAC,EACzC;IAAE0G,KAAK,EAAE,KAAK;IAAE1G,WAAW,EAAE;EAAS,CAAC,EACvC;IAAE0G,KAAK,EAAE,MAAM;IAAE1G,WAAW,EAAE;EAAS,CAAC,EACxC;IAAE0G,KAAK,EAAE,MAAM;IAAE1G,WAAW,EAAE;EAAS,CAAC,EACxC;IAAE0G,KAAK,EAAE,IAAI;IAAE1G,WAAW,EAAE;EAAO,CAAC,CACrC;EAED,oBACEjD,OAAA;IAAA4J,QAAA,gBACE5J,OAAA;MAAKyJ,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,eAC/B5J,OAAA,CAACvC,KAAK;QAAAmM,QAAA,eACJ5J,OAAA,CAACxC,MAAM;UACLmF,IAAI,eAAE3C,OAAA,CAACpB,iBAAiB;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5B+G,OAAO,EAAEA,CAAA,KAAMnJ,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAAiJ,QAAA,EAC7B;QAED;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN/C,OAAA,CAACtC,GAAG;MAACqM,MAAM,EAAE,EAAG;MAAAH,QAAA,gBACd5J,OAAA,CAACrC,GAAG;QAACqM,IAAI,EAAE,EAAG;QAAAJ,QAAA,eACZ5J,OAAA,CAACzC,IAAI;UAACoM,KAAK,eACT3J,OAAA,CAACvC,KAAK;YAAAmM,QAAA,gBACJ5J,OAAA,CAACnB,YAAY;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChB/C,OAAA;cAAA4J,QAAA,EAAM;YAAI;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UAAA6G,QAAA,eACC5J,OAAA,CAAC9B,IAAI;YAAC+L,gBAAgB,EAAC,QAAQ;YAAAL,QAAA,gBAC7B5J,OAAA,CAAC9B,IAAI,CAACgM,OAAO;cAACC,GAAG,EAAC,0BAAM;cAAAP,QAAA,gBACtB5J,OAAA;gBAAKyJ,KAAK,EAAE;kBAAEI,YAAY,EAAE;gBAAG,CAAE;gBAAAD,QAAA,gBAC/B5J,OAAA,CAAC/B,KAAK;kBACJK,OAAO,EAAC,sCAAQ;kBAChB2E,WAAW,EAAC,sLAAgC;kBAC5CK,IAAI,EAAC,MAAM;kBACX8G,QAAQ;kBACRX,KAAK,EAAE;oBAAEI,YAAY,EAAE;kBAAG;gBAAE;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eAEF/C,OAAA;kBAAKyJ,KAAK,EAAE;oBAAEI,YAAY,EAAE;kBAAG,CAAE;kBAAAD,QAAA,gBAC/B5J,OAAA,CAACK,IAAI;oBAACgK,MAAM;oBAAAT,QAAA,EAAC;kBAAK;oBAAAhH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzB/C,OAAA,CAAC7B,MAAM;oBACLsE,KAAK,EAAEjB,YAAa;oBACpB8I,QAAQ,EAAE7I,eAAgB;oBAC1BgI,KAAK,EAAE;sBAAEvB,KAAK,EAAE,GAAG;sBAAEqC,UAAU,EAAE;oBAAE,CAAE;oBAAAX,QAAA,EAEpCpH,cAAc,CAACgI,GAAG,CAAClH,IAAI,iBACtBtD,OAAA,CAACM,MAAM;sBAAkBmC,KAAK,EAAEa,IAAI,CAACb,KAAM;sBAAAmH,QAAA,eACzC5J,OAAA,CAACvC,KAAK;wBAAAmM,QAAA,GACHtG,IAAI,CAACX,IAAI,EACTW,IAAI,CAACZ,KAAK;sBAAA;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC,GAJGO,IAAI,CAACb,KAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKf,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/C,OAAA,CAACpC,MAAM,CAAC6M,OAAO;gBACblE,IAAI,EAAC,MAAM;gBACXmE,QAAQ;gBACRC,MAAM,EAAEnJ,YAAY,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;gBACzDoJ,aAAa,EAAErF,YAAa;gBAC5BsF,SAAS,EAAE5D,aAAc;gBACzBrG,QAAQ,EAAEA,QAAS;gBACnB0J,QAAQ,EAAEA,CAAC;kBAAE1J;gBAAS,CAAC,KAAKC,WAAW,CAACD,QAAQ,CAAE;gBAClDkK,QAAQ,EAAE5J,WAAY;gBAAA0I,QAAA,gBAEtB5J,OAAA;kBAAG+K,SAAS,EAAC,sBAAsB;kBAAAnB,QAAA,eACjC5J,OAAA,CAAClB,cAAc;oBAAC2K,KAAK,EAAE;sBAAEuB,QAAQ,EAAE,EAAE;sBAAEhI,KAAK,EAAE;oBAAU;kBAAE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACJ/C,OAAA;kBAAG+K,SAAS,EAAC,iBAAiB;kBAAAnB,QAAA,GAAC,gCACxB,EAACpI,YAAY,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,EAAC,kDAC/C;gBAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ/C,OAAA;kBAAG+K,SAAS,EAAC,iBAAiB;kBAAAnB,QAAA,GAAC,eAC1B,EAACpI,YAAY,KAAK,OAAO,GAAG,aAAa,GAAG,aAAa,EAAC,qBAC/D;gBAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,EAEhB7B,WAAW,iBACVlB,OAAA;gBAAKyJ,KAAK,EAAE;kBAAEwB,SAAS,EAAE,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAtB,QAAA,gBACjD5J,OAAA,CAAChC,QAAQ;kBAACsF,IAAI,EAAC,QAAQ;kBAAC6H,OAAO,EAAE;gBAAG;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvC/C,OAAA;kBAAKyJ,KAAK,EAAE;oBAAEwB,SAAS,EAAE;kBAAE,CAAE;kBAAArB,QAAA,eAC3B5J,OAAA,CAACK,IAAI;oBAAAuJ,QAAA,EAAC;kBAAiB;oBAAAhH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GAzD0B,QAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0DvB,CAAC,eAEf/C,OAAA,CAAC9B,IAAI,CAACgM,OAAO;cAACC,GAAG,EAAC,0BAAM;cAAAP,QAAA,eACtB5J,OAAA;gBAAKyJ,KAAK,EAAE;kBAAEyB,SAAS,EAAE;gBAAS,CAAE;gBAAAtB,QAAA,EACjCpI,YAAY,KAAK,OAAO,gBACvBxB,OAAA;kBAAA4J,QAAA,gBACE5J,OAAA;oBAAKyJ,KAAK,EAAE;sBAAEI,YAAY,EAAE;oBAAG,CAAE;oBAAAD,QAAA,eAC/B5J,OAAA;sBAAKyJ,KAAK,EAAE;wBACVvB,KAAK,EAAE,GAAG;wBACVC,MAAM,EAAE,GAAG;wBACXiD,YAAY,EAAE,KAAK;wBACnBC,UAAU,EAAEnJ,WAAW,GACrB,0EAA0E,GAC1E,SAAS;wBACXoJ,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,QAAQ;wBACxBC,MAAM,EAAE,QAAQ;wBAChBC,MAAM,EAAExJ,WAAW,GAAG,mBAAmB,GAAG,mBAAmB;wBAC/DyJ,UAAU,EAAE;sBACd,CAAE;sBAAA/B,QAAA,eACA5J,OAAA,CAACR,aAAa;wBAACiK,KAAK,EAAE;0BACpBuB,QAAQ,EAAE,EAAE;0BACZhI,KAAK,EAAEd,WAAW,GAAG,SAAS,GAAG;wBACnC;sBAAE;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELb,WAAW,iBACVlC,OAAA;oBAAKyJ,KAAK,EAAE;sBAAEI,YAAY,EAAE;oBAAG,CAAE;oBAAAD,QAAA,eAC/B5J,OAAA,CAACrB,KAAK;sBAACyJ,OAAO,EAAEpG,WAAY;sBAACyE,IAAI,EAAC,OAAO;sBAAAmD,QAAA,EACtCF,YAAY,CAACc,GAAG,CAAC,CAACoB,IAAI,EAAEC,KAAK,kBAC5B7L,OAAA,CAACrB,KAAK,CAACmN,IAAI;wBAAanC,KAAK,EAAEiC,IAAI,CAACjC,KAAM;wBAAC1G,WAAW,EAAE2I,IAAI,CAAC3I;sBAAY,GAAxD4I,KAAK;wBAAAjJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAqD,CAC5E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN,eAED/C,OAAA,CAACvC,KAAK;oBAAAmM,QAAA,EACH,CAAC1H,WAAW,gBACXlC,OAAA,CAACxC,MAAM;sBACL8F,IAAI,EAAC,SAAS;sBACdmD,IAAI,EAAC,OAAO;sBACZ9D,IAAI,eAAE3C,OAAA,CAACJ,kBAAkB;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC7B+G,OAAO,EAAEjB,YAAa;sBAAAe,QAAA,EACvB;oBAED;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAET/C,OAAA,CAACxC,MAAM;sBACLiJ,IAAI,EAAC,OAAO;sBACZsF,MAAM;sBACNpJ,IAAI,eAAE3C,OAAA,CAACH,YAAY;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACvB+G,OAAO,EAAEA,CAAA,KAAM3H,cAAc,CAAC,KAAK,CAAE;sBAAAyH,QAAA,EACtC;oBAED;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBACT;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,gBAEN/C,OAAA;kBAAA4J,QAAA,gBACE5J,OAAA;oBAAKyJ,KAAK,EAAE;sBAAEI,YAAY,EAAE;oBAAG,CAAE;oBAAAD,QAAA,gBAC/B5J,OAAA;sBACEgM,GAAG,EAAE5J,SAAU;sBACf6J,QAAQ;sBACRC,WAAW;sBACXzC,KAAK,EAAE;wBACLvB,KAAK,EAAE,MAAM;wBACbiE,QAAQ,EAAE,GAAG;wBACbhE,MAAM,EAAE,MAAM;wBACduD,MAAM,EAAE,mBAAmB;wBAC3BN,YAAY,EAAE,CAAC;wBACfE,OAAO,EAAEhJ,cAAc,GAAG,OAAO,GAAG;sBACtC;oBAAE;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF/C,OAAA;sBAAQgM,GAAG,EAAE3J,SAAU;sBAACoH,KAAK,EAAE;wBAAE6B,OAAO,EAAE;sBAAO;oBAAE;sBAAA1I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAErD,CAACT,cAAc,iBACdtC,OAAA;sBAAKyJ,KAAK,EAAE;wBACVvB,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,GAAG;wBACXuD,MAAM,EAAE,oBAAoB;wBAC5BN,YAAY,EAAE,CAAC;wBACfE,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,QAAQ;wBACxBY,aAAa,EAAE,QAAQ;wBACvBpJ,KAAK,EAAE;sBACT,CAAE;sBAAA4G,QAAA,gBACA5J,OAAA,CAACjB,cAAc;wBAAC0K,KAAK,EAAE;0BAAEuB,QAAQ,EAAE,EAAE;0BAAEnB,YAAY,EAAE;wBAAG;sBAAE;wBAAAjH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7D/C,OAAA,CAACK,IAAI;wBAACiD,IAAI,EAAC,WAAW;wBAAAsG,QAAA,EAAC;sBAAW;wBAAAhH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAELb,WAAW,iBACVlC,OAAA;oBAAKyJ,KAAK,EAAE;sBAAEI,YAAY,EAAE;oBAAG,CAAE;oBAAAD,QAAA,eAC/B5J,OAAA,CAACrB,KAAK;sBAACyJ,OAAO,EAAEpG,WAAY;sBAACyE,IAAI,EAAC,OAAO;sBAAAmD,QAAA,EACtCF,YAAY,CAACc,GAAG,CAAC,CAACoB,IAAI,EAAEC,KAAK,kBAC5B7L,OAAA,CAACrB,KAAK,CAACmN,IAAI;wBAAanC,KAAK,EAAEiC,IAAI,CAACjC,KAAM;wBAAC1G,WAAW,EAAE2I,IAAI,CAAC3I;sBAAY,GAAxD4I,KAAK;wBAAAjJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAqD,CAC5E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN,eAED/C,OAAA,CAACvC,KAAK;oBAAAmM,QAAA,EACH,CAACtH,cAAc,gBACdtC,OAAA,CAACxC,MAAM;sBACL8F,IAAI,EAAC,SAAS;sBACdX,IAAI,eAAE3C,OAAA,CAACjB,cAAc;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzB+G,OAAO,EAAElC,WAAY;sBAAAgC,QAAA,EACtB;oBAED;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAET/C,OAAA,CAAAE,SAAA;sBAAA0J,QAAA,gBACE5J,OAAA,CAACxC,MAAM;wBACL8F,IAAI,EAAC,SAAS;wBACdX,IAAI,eAAE3C,OAAA,CAACZ,YAAY;0BAAAwD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvB+G,OAAO,EAAEjB,YAAa;wBACtBwD,OAAO,EAAEnK,WAAY;wBACrB4I,QAAQ,EAAE5I,WAAY;wBAAA0H,QAAA,EACvB;sBAED;wBAAAhH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT/C,OAAA,CAACxC,MAAM;wBACLmF,IAAI,eAAE3C,OAAA,CAAChB,cAAc;0BAAA4D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACzB+G,OAAO,EAAExB,UAAW;wBAAAsB,QAAA,EACrB;sBAED;wBAAAhH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAvIqB,SAAS;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwIxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN/C,OAAA,CAACrC,GAAG;QAACqM,IAAI,EAAE,CAAE;QAAAJ,QAAA,gBACX5J,OAAA,CAACzC,IAAI;UAACoM,KAAK,EAAC,0BAAM;UAAClD,IAAI,EAAC,OAAO;UAAAmD,QAAA,eAC7B5J,OAAA,CAACvC,KAAK;YAAC6O,SAAS,EAAC,UAAU;YAAC7C,KAAK,EAAE;cAAEvB,KAAK,EAAE;YAAO,CAAE;YAAA0B,QAAA,gBACnD5J,OAAA;cAAA4J,QAAA,gBACE5J,OAAA,CAACK,IAAI;gBAACgK,MAAM;gBAAAT,QAAA,EAAC;cAAI;gBAAAhH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxB/C,OAAA,CAAC7B,MAAM;gBACLsE,KAAK,EAAEjB,YAAa;gBACpB8I,QAAQ,EAAE7I,eAAgB;gBAC1BgI,KAAK,EAAE;kBAAEvB,KAAK,EAAE,MAAM;kBAAE+C,SAAS,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAEtCpH,cAAc,CAACgI,GAAG,CAAClH,IAAI,iBACtBtD,OAAA,CAACM,MAAM;kBAAkBmC,KAAK,EAAEa,IAAI,CAACb,KAAM;kBAAAmH,QAAA,eACzC5J,OAAA,CAACvC,KAAK;oBAAAmM,QAAA,GACHtG,IAAI,CAACX,IAAI,EACTW,IAAI,CAACZ,KAAK;kBAAA;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC,GAJGO,IAAI,CAACb,KAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACT/C,OAAA;gBAAKyJ,KAAK,EAAE;kBAAEwB,SAAS,EAAE,CAAC;kBAAED,QAAQ,EAAE,EAAE;kBAAEhI,KAAK,EAAE;gBAAO,CAAE;gBAAA4G,QAAA,GAAAnJ,oBAAA,GACvD+B,cAAc,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,KAAK,KAAKjB,YAAY,CAAC,cAAAf,oBAAA,uBAAlDA,oBAAA,CAAoDwC;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/C,OAAA;cAAA4J,QAAA,gBACE5J,OAAA,CAACK,IAAI;gBAACgK,MAAM;gBAAAT,QAAA,EAAC;cAAK;gBAAAhH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzB/C,OAAA,CAAC3B,MAAM;gBACLoE,KAAK,EAAEf,UAAW;gBAClB4I,QAAQ,EAAE3I,aAAc;gBACxB8H,KAAK,EAAE;kBAAEwB,SAAS,EAAE;gBAAE,CAAE;gBACxBsB,OAAO,EAAE;kBAAEC,SAAS,EAAG/J,KAAK,IAAK,GAAGA,KAAK;gBAAI;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF/C,OAAA,CAACK,IAAI;gBAACiD,IAAI,EAAC,WAAW;gBAACmG,KAAK,EAAE;kBAAEuB,QAAQ,EAAE;gBAAG,CAAE;gBAAApB,QAAA,GAAC,gBAC1C,EAAClI,UAAU,EAAC,GAClB;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN/C,OAAA;cAAA4J,QAAA,eACE5J,OAAA,CAACvC,KAAK;gBAACgM,KAAK,EAAE;kBAAEvB,KAAK,EAAE,MAAM;kBAAEsD,cAAc,EAAE;gBAAgB,CAAE;gBAAA5B,QAAA,gBAC/D5J,OAAA,CAACK,IAAI;kBAACgK,MAAM;kBAAAT,QAAA,EAAC;gBAAI;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxB/C,OAAA,CAAC5B,MAAM;kBACLqO,OAAO,EAAE7K,cAAe;kBACxB0I,QAAQ,EAAEzI;gBAAkB;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN/C,OAAA;cAAA4J,QAAA,eACE5J,OAAA,CAACvC,KAAK;gBAACgM,KAAK,EAAE;kBAAEvB,KAAK,EAAE,MAAM;kBAAEsD,cAAc,EAAE;gBAAgB,CAAE;gBAAA5B,QAAA,gBAC/D5J,OAAA,CAACK,IAAI;kBAACgK,MAAM;kBAAAT,QAAA,EAAC;gBAAI;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxB/C,OAAA,CAAC5B,MAAM;kBACLqO,OAAO,EAAE3K,kBAAmB;kBAC5BwI,QAAQ,EAAEvI;gBAAsB;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP/C,OAAA,CAACzC,IAAI;UAACoM,KAAK,EAAC,0BAAM;UAAClD,IAAI,EAAC,OAAO;UAACgD,KAAK,EAAE;YAAEwB,SAAS,EAAE;UAAG,CAAE;UAAArB,QAAA,GAAAlJ,qBAAA,GACtD8B,cAAc,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,KAAK,KAAKjB,YAAY,CAAC,cAAAd,qBAAA,uBAAlDA,qBAAA,CAAoDwC,QAAQ,CAACsH,GAAG,CAAC,CAACkC,OAAO,EAAEb,KAAK,kBAC/E7L,OAAA,CAACjC,GAAG;YAAa0L,KAAK,EAAE;cAAEI,YAAY,EAAE;YAAE,CAAE;YAAAD,QAAA,EACzC8C;UAAO,GADAb,KAAK;YAAAjJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEP/C,OAAA,CAACzC,IAAI;UAACoM,KAAK,EAAC,0BAAM;UAAClD,IAAI,EAAC,OAAO;UAACgD,KAAK,EAAE;YAAEwB,SAAS,EAAE;UAAG,CAAE;UAAArB,QAAA,eACvD5J,OAAA,CAACtC,GAAG;YAACqM,MAAM,EAAE,EAAG;YAAAH,QAAA,gBACd5J,OAAA,CAACrC,GAAG;cAACqM,IAAI,EAAE,EAAG;cAAAJ,QAAA,eACZ5J,OAAA,CAACvB,SAAS;gBACRkL,KAAK,EAAC,oBAAK;gBACXlH,KAAK,EAAE3B,QAAQ,CAACkI,MAAO;gBACvB2D,MAAM,eAAE3M,OAAA,CAACnB,YAAY;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB6J,MAAM,EAAC;cAAG;gBAAAhK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA,CAACrC,GAAG;cAACqM,IAAI,EAAE,EAAG;cAAAJ,QAAA,eACZ5J,OAAA,CAACvB,SAAS;gBACRkL,KAAK,EAAC,0BAAM;gBACZlH,KAAK,EAAE3B,QAAQ,CAACkI,MAAM,GAAG,CAAC,GACxB,CAAClI,QAAQ,CAAC+L,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAClG,YAAY,EAAE,CAAC,CAAC,GAAG/F,QAAQ,CAACkI,MAAM,GAAG,GAAG,EAAEgE,OAAO,CAAC,CAAC,CAAC,GACzF,CACD;gBACDJ,MAAM,EAAC,GAAG;gBACVD,MAAM,eAAE3M,OAAA,CAACZ,YAAY;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA,CAACzC,IAAI;MACHoM,KAAK,eACH3J,OAAA,CAACvC,KAAK;QAAAmM,QAAA,gBACJ5J,OAAA,CAACnB,YAAY;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChB/C,OAAA;UAAA4J,QAAA,EAAM;QAAI;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjB/C,OAAA,CAACtB,KAAK;UAACuO,KAAK,EAAEnM,QAAQ,CAACkI;QAAO;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACR;MACDmK,KAAK,eACHlN,OAAA,CAACvC,KAAK;QAAAmM,QAAA,gBACJ5J,OAAA,CAACxC,MAAM;UACLmF,IAAI,eAAE3C,OAAA,CAACf,gBAAgB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3B+G,OAAO,EAAEA,CAAA,KAAMxL,OAAO,CAACsK,IAAI,CAAC,SAAS,CAAE;UAAAgB,QAAA,EACxC;QAED;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/C,OAAA,CAACxC,MAAM;UACLmF,IAAI,eAAE3C,OAAA,CAACb,aAAa;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB+G,OAAO,EAAEb,QAAS;UAClB6B,QAAQ,EAAEhK,QAAQ,CAACkI,MAAM,KAAK,CAAE;UAAAY,QAAA,EACjC;QAED;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MACD0G,KAAK,EAAE;QAAEwB,SAAS,EAAE;MAAG,CAAE;MAAArB,QAAA,EAExB9I,QAAQ,CAACkI,MAAM,KAAK,CAAC,gBACpBhJ,OAAA;QAAKyJ,KAAK,EAAE;UAAEyB,SAAS,EAAE,QAAQ;UAAEiC,OAAO,EAAE,QAAQ;UAAEnK,KAAK,EAAE;QAAO,CAAE;QAAA4G,QAAA,gBACpE5J,OAAA,CAACnB,YAAY;UAAC4K,KAAK,EAAE;YAAEuB,QAAQ,EAAE,EAAE;YAAEnB,YAAY,EAAE;UAAG;QAAE;UAAAjH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D/C,OAAA;UAAA4J,QAAA,EAAK;QAAM;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjB/C,OAAA;UAAKyJ,KAAK,EAAE;YAAEuB,QAAQ,EAAE;UAAG,CAAE;UAAApB,QAAA,EAAC;QAAgB;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,gBAEN/C,OAAA,CAACtC,GAAG;QAACqM,MAAM,EAAE,EAAG;QAAAH,QAAA,gBACd5J,OAAA,CAACrC,GAAG;UAACqM,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACX5J,OAAA,CAAClC,IAAI;YACH2I,IAAI,EAAC,OAAO;YACZ2G,UAAU,EAAEtM,QAAS;YACrBuM,UAAU,EAAGC,IAAI,iBACftN,OAAA,CAAClC,IAAI,CAACyP,IAAI;cACR9D,KAAK,EAAE;gBACL+D,MAAM,EAAE,SAAS;gBACjBC,eAAe,EAAE,CAAAzM,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqC,EAAE,MAAKiK,IAAI,CAACjK,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC7E8J,OAAO,EAAE,UAAU;gBACnB/B,YAAY,EAAE;cAChB,CAAE;cACFtB,OAAO,EAAEA,CAAA,KAAM7I,mBAAmB,CAACqM,IAAI,CAAE;cAAA1D,QAAA,eAEzC5J,OAAA,CAAClC,IAAI,CAACyP,IAAI,CAACG,IAAI;gBACbC,MAAM,eACJ3N,OAAA;kBAAKyJ,KAAK,EAAE;oBACVvB,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACViD,YAAY,EAAE,KAAK;oBACnBqC,eAAe,EAAEnE,YAAY,CAACgE,IAAI,CAAChK,IAAI,CAAC;oBACxCgI,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBxI,KAAK,EAAE;kBACT,CAAE;kBAAA4G,QAAA,EACCV,WAAW,CAACoE,IAAI,CAAChK,IAAI;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CACN;gBACD4G,KAAK,eACH3J,OAAA,CAACvC,KAAK;kBAAAmM,QAAA,gBACJ5J,OAAA,CAACK,IAAI;oBAACgK,MAAM;oBAACZ,KAAK,EAAE;sBAAEuB,QAAQ,EAAE;oBAAG,CAAE;oBAAApB,QAAA,EAClC0D,IAAI,CAAChH,QAAQ,IAAI,GAAGiD,YAAY,CAAC+D,IAAI,CAAChK,IAAI,CAAC;kBAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACP/C,OAAA,CAACjC,GAAG;oBAACiF,KAAK,EAAEsG,YAAY,CAACgE,IAAI,CAAChK,IAAI,CAAE;oBAAAsG,QAAA,EACjCL,YAAY,CAAC+D,IAAI,CAAChK,IAAI;kBAAC;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR;gBACDE,WAAW,eACTjD,OAAA;kBAAA4J,QAAA,gBACE5J,OAAA,CAACK,IAAI;oBAACiD,IAAI,EAAC,WAAW;oBAACmG,KAAK,EAAE;sBAAEuB,QAAQ,EAAE;oBAAG,CAAE;oBAAApB,QAAA,EAC5C0D,IAAI,CAACzJ;kBAAS;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACP/C,OAAA;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/C,OAAA,CAACK,IAAI;oBAACiD,IAAI,EAAC,WAAW;oBAACmG,KAAK,EAAE;sBAAEuB,QAAQ,EAAE;oBAAG,CAAE;oBAAApB,QAAA,GAAC,gBAC1C,EAAC,CAAC0D,IAAI,CAACzG,YAAY,GAAG,GAAG,EAAEmG,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5C;kBAAA;oBAAApK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/C,OAAA,CAACrC,GAAG;UAACqM,IAAI,EAAE,EAAG;UAAAJ,QAAA,EACX5I,gBAAgB,iBACfhB,OAAA;YAAA4J,QAAA,GACG5I,gBAAgB,CAACwF,GAAG,iBACnBxG,OAAA;cAAKyJ,KAAK,EAAE;gBAAEI,YAAY,EAAE,EAAE;gBAAEqB,SAAS,EAAE;cAAS,CAAE;cAAAtB,QAAA,eACpD5J,OAAA,CAACnC,KAAK;gBACJ+P,GAAG,EAAE5M,gBAAgB,CAACwF,GAAI;gBAC1BqH,GAAG,EAAE7M,gBAAgB,CAACsF,QAAS;gBAC/BmD,KAAK,EAAE;kBAAE0C,QAAQ,EAAE,MAAM;kBAAE2B,SAAS,EAAE;gBAAI,CAAE;gBAC5C5G,OAAO,EAAE;kBACPvD,IAAI,eACF3D,OAAA,CAACvC,KAAK;oBAAAmM,QAAA,gBACJ5J,OAAA,CAACd,cAAc;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEpB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAEX;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAED/C,OAAA,CAACxB,OAAO;cAACuP,WAAW,EAAC,MAAM;cAAAnE,QAAA,EAAC;YAAI;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAE1C/C,OAAA,CAAClC,IAAI;cACHsP,UAAU,EAAEpM,gBAAgB,CAACkF,OAAQ;cACrCmH,UAAU,EAAG3F,MAAM,iBACjB1H,OAAA,CAAClC,IAAI,CAACyP,IAAI;gBAAA3D,QAAA,eACR5J,OAAA,CAAClC,IAAI,CAACyP,IAAI,CAACG,IAAI;kBACbC,MAAM,EAAEnE,aAAa,CAAC9B,MAAM,CAAC1D,MAAM,CAAE;kBACrC2F,KAAK,eACH3J,OAAA,CAACvC,KAAK;oBAAAmM,QAAA,gBACJ5J,OAAA,CAACK,IAAI;sBAACgK,MAAM;sBAAAT,QAAA,EAAEL,YAAY,CAAC7B,MAAM,CAACpE,IAAI;oBAAC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC/C/C,OAAA,CAACjC,GAAG;sBAACiF,KAAK,EAAE0E,MAAM,CAAChG,UAAU,IAAI,GAAG,GAAG,OAAO,GAAGgG,MAAM,CAAChG,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAM;sBAAAkI,QAAA,GAAC,sBACvF,EAAC,CAAClC,MAAM,CAAChG,UAAU,GAAG,GAAG,EAAEsL,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;oBAAA;sBAAApK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACL2E,MAAM,CAACzD,UAAU,iBAChBjE,OAAA,CAACjC,GAAG;sBAACiF,KAAK,EAAC,MAAM;sBAAA4G,QAAA,GAAC,sBACX,EAAC,CAAClC,MAAM,CAACzD,UAAU,GAAG,GAAG,EAAE+I,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;oBAAA;sBAAApK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CACN,EACA2E,MAAM,CAACxD,QAAQ,KAAK8J,SAAS,iBAC5BhO,OAAA,CAACjC,GAAG;sBAACiF,KAAK,EAAE0E,MAAM,CAACxD,QAAQ,GAAG,OAAO,GAAG,KAAM;sBAAA0F,QAAA,EAC3ClC,MAAM,CAACxD,QAAQ,GAAG,IAAI,GAAG;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CACR;kBACDE,WAAW,eACTjD,OAAA;oBAAA4J,QAAA,gBACE5J,OAAA;sBAAKyJ,KAAK,EAAE;wBAAEI,YAAY,EAAE;sBAAE,CAAE;sBAAAD,QAAA,eAC9B5J,OAAA,CAACK,IAAI;wBAACiD,IAAI,EAAC,WAAW;wBAACmG,KAAK,EAAE;0BAAEuB,QAAQ,EAAE;wBAAG,CAAE;wBAAApB,QAAA,EAC5ClC,MAAM,CAAC7D;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN/C,OAAA;sBAAA4J,QAAA,EACGqE,MAAM,CAACC,OAAO,CAACxG,MAAM,CAACxE,QAAQ,CAAC,CAACsH,GAAG,CAAC,CAAC,CAAC2D,GAAG,EAAE1L,KAAK,CAAC,kBAChDzC,OAAA,CAACjC,GAAG;wBAAW0L,KAAK,EAAE;0BAAEI,YAAY,EAAE;wBAAE,CAAE;wBAAAD,QAAA,GACvCuE,GAAG,EAAC,IAAE,EAAC1L,KAAK;sBAAA,GADL0L,GAAG;wBAAAvL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAER,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGP/C,OAAA,CAACzB,KAAK;MACJ6P,IAAI,EAAEhN,cAAe;MACrBuI,KAAK,EAAC,0BAAM;MACZ0E,MAAM,EAAE,IAAK;MACbC,QAAQ,EAAEA,CAAA,KAAMjN,iBAAiB,CAAC,KAAK,CAAE;MACzC6G,KAAK,EAAE,GAAI;MAAA0B,QAAA,eAEX5J,OAAA;QAAK6N,GAAG,EAAC,SAAS;QAACpE,KAAK,EAAE;UAAEvB,KAAK,EAAE;QAAO,CAAE;QAAC0F,GAAG,EAAEtM;MAAa;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvC,EAAA,CA7xBID,oBAA8B;EAAA,QACjBT,WAAW;AAAA;AAAAyO,EAAA,GADxBhO,oBAA8B;AA+xBpC,eAAeA,oBAAoB;AAAC,IAAAgO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}