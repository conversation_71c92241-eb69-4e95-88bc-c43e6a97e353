import React, { useState, useRef, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  Space,
  Row,
  Col,
  Upload,
  Image,
  List,
  Tag,
  Progress,
  Alert,
  Tabs,
  Select,
  Switch,
  Slider,
  message,
  Modal,
  Divider,
  Statistic,
  Badge,
  Tooltip,
} from 'antd';
import {
  ArrowLeftOutlined,
  EyeOutlined,
  UploadOutlined,
  CameraOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ClearOutlined,
  ScanOutlined,
  PictureOutlined,
  UserOutlined,
  CarOutlined,
  HomeOutlined,
  ShoppingOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

interface RecognitionResult {
  id: string;
  type: 'object' | 'face' | 'text' | 'scene' | 'landmark';
  label: string;
  confidence: number;
  bbox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  attributes?: Record<string, any>;
}

interface ImageAnalysis {
  id: string;
  filename: string;
  url: string;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
  results: RecognitionResult[];
  timestamp: string;
  processingTime: number;
}

const VisionRecognition: React.FC = () => {
  const navigate = useNavigate();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [analyses, setAnalyses] = useState<ImageAnalysis[]>([]);
  const [selectedImage, setSelectedImage] = useState<ImageAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['object', 'face', 'text', 'scene']);
  const [confidence, setConfidence] = useState(70);
  const [enableRealtime, setEnableRealtime] = useState(true);
  const [maxResults, setMaxResults] = useState(10);

  const cameraRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  // 识别类型配置
  const recognitionTypes = [
    { value: 'object', label: '物体识别', icon: <ScanOutlined />, color: '#1890ff' },
    { value: 'face', label: '人脸识别', icon: <UserOutlined />, color: '#52c41a' },
    { value: 'text', label: '文字识别', icon: <PictureOutlined />, color: '#faad14' },
    { value: 'scene', label: '场景识别', icon: <HomeOutlined />, color: '#722ed1' },
    { value: 'landmark', label: '地标识别', icon: <CarOutlined />, color: '#eb2f96' },
  ];

  // 模拟识别结果
  const mockResults: RecognitionResult[] = [
    {
      id: '1',
      type: 'object',
      label: '汽车',
      confidence: 0.95,
      bbox: { x: 100, y: 50, width: 200, height: 150 },
      attributes: { color: '红色', brand: '丰田' },
    },
    {
      id: '2',
      type: 'face',
      label: '人脸',
      confidence: 0.88,
      bbox: { x: 300, y: 80, width: 80, height: 100 },
      attributes: { age: '25-35', gender: '女性', emotion: '微笑' },
    },
    {
      id: '3',
      type: 'text',
      label: '停车场',
      confidence: 0.92,
      bbox: { x: 50, y: 20, width: 120, height: 30 },
    },
    {
      id: '4',
      type: 'scene',
      label: '城市街道',
      confidence: 0.85,
    },
  ];

  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      setIsAnalyzing(true);

      // 模拟文件上传和分析
      await new Promise(resolve => setTimeout(resolve, 2000));

      const imageUrl = URL.createObjectURL(file as File);
      const newAnalysis: ImageAnalysis = {
        id: Date.now().toString(),
        filename: (file as File).name,
        url: imageUrl,
        size: (file as File).size,
        dimensions: { width: 800, height: 600 }, // 模拟尺寸
        results: mockResults.filter(r => selectedTypes.includes(r.type)),
        timestamp: new Date().toLocaleString(),
        processingTime: 1.5 + Math.random() * 2,
      };

      setAnalyses(prev => [newAnalysis, ...prev]);
      setSelectedImage(newAnalysis);

      onSuccess?.(newAnalysis);
      message.success(`${(file as File).name} 分析完成`);
    } catch (error) {
      onError?.(error as Error);
      message.error('分析失败，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as File);
    }
    setPreviewImage(file.url || file.preview || '');
    setPreviewVisible(true);
  };

  const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 }
      });
      if (cameraRef.current) {
        cameraRef.current.srcObject = stream;
        setIsCameraActive(true);
        message.success('摄像头已启动');
      }
    } catch (error) {
      message.error('无法访问摄像头，请检查权限设置');
    }
  };

  const stopCamera = () => {
    if (cameraRef.current?.srcObject) {
      const stream = cameraRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      cameraRef.current.srcObject = null;
      setIsCameraActive(false);
      message.info('摄像头已关闭');
    }
  };

  const capturePhoto = () => {
    if (cameraRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = cameraRef.current;
      const context = canvas.getContext('2d');

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context?.drawImage(video, 0, 0);

      canvas.toBlob(blob => {
        if (blob) {
          const file = new File([blob], `capture_${Date.now()}.jpg`, { type: 'image/jpeg' });
          handleUpload({
            file,
            onSuccess: () => {},
            onError: () => {},
          } as any);
        }
      });
    }
  };

  const clearAll = () => {
    setAnalyses([]);
    setSelectedImage(null);
    setFileList([]);
    message.success('已清空所有分析结果');
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = recognitionTypes.find(t => t.value === type);
    return typeConfig?.icon || <ScanOutlined />;
  };

  const getTypeColor = (type: string) => {
    const typeConfig = recognitionTypes.find(t => t.value === type);
    return typeConfig?.color || '#1890ff';
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
          >
            返回
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          <Card title={
            <Space>
              <EyeOutlined />
              <span>视觉识别</span>
            </Space>
          }>
            <Tabs defaultActiveKey="upload">
              <Tabs.TabPane tab="文件上传" key="upload">
                <Upload.Dragger
                  name="file"
                  multiple
                  accept="image/*"
                  customRequest={handleUpload}
                  onPreview={handlePreview}
                  fileList={fileList}
                  onChange={({ fileList }) => setFileList(fileList)}
                  disabled={isAnalyzing}
                >
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  </p>
                  <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持 JPG、PNG、GIF 等格式，单个文件不超过 10MB
                  </p>
                </Upload.Dragger>

                {isAnalyzing && (
                  <div style={{ marginTop: 16, textAlign: 'center' }}>
                    <Progress type="circle" percent={75} />
                    <div style={{ marginTop: 8 }}>
                      <Text>正在分析图片，请稍候...</Text>
                    </div>
                  </div>
                )}
              </Tabs.TabPane>

              <Tabs.TabPane tab="摄像头拍照" key="camera">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ marginBottom: 16 }}>
                    <video
                      ref={cameraRef}
                      autoPlay
                      playsInline
                      style={{
                        width: '100%',
                        maxWidth: 640,
                        height: 'auto',
                        border: '1px solid #d9d9d9',
                        borderRadius: 6,
                        display: isCameraActive ? 'block' : 'none',
                      }}
                    />
                    <canvas ref={canvasRef} style={{ display: 'none' }} />

                    {!isCameraActive && (
                      <div style={{
                        width: '100%',
                        height: 300,
                        border: '1px dashed #d9d9d9',
                        borderRadius: 6,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: '#999',
                      }}>
                        <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                        <Text type="secondary">点击下方按钮启动摄像头</Text>
                      </div>
                    )}
                  </div>

                  <Space>
                    {!isCameraActive ? (
                      <Button
                        type="primary"
                        icon={<CameraOutlined />}
                        onClick={startCamera}
                      >
                        启动摄像头
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="primary"
                          icon={<CameraOutlined />}
                          onClick={capturePhoto}
                          loading={isAnalyzing}
                        >
                          拍照识别
                        </Button>
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={stopCamera}
                        >
                          关闭摄像头
                        </Button>
                      </>
                    )}
                  </Space>
                </div>
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Col>

        <Col span={8}>
          <Card title="识别设置" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>识别类型</Text>
                <Select
                  mode="multiple"
                  value={selectedTypes}
                  onChange={setSelectedTypes}
                  style={{ width: '100%', marginTop: 8 }}
                  placeholder="选择识别类型"
                >
                  {recognitionTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      <Space>
                        {type.icon}
                        {type.label}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <Text strong>置信度阈值</Text>
                <Slider
                  value={confidence}
                  onChange={setConfidence}
                  style={{ marginTop: 8 }}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  当前: {confidence}%
                </Text>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>实时分析</Text>
                  <Switch
                    checked={enableRealtime}
                    onChange={setEnableRealtime}
                  />
                </Space>
              </div>

              <div>
                <Text strong>最大结果数</Text>
                <Select
                  value={maxResults}
                  onChange={setMaxResults}
                  style={{ width: '100%', marginTop: 8 }}
                >
                  <Option value={5}>5个</Option>
                  <Option value={10}>10个</Option>
                  <Option value={20}>20个</Option>
                  <Option value={50}>50个</Option>
                </Select>
              </div>
            </Space>
          </Card>

          <Card title="统计信息" size="small" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="已分析"
                  value={analyses.length}
                  prefix={<PictureOutlined />}
                  suffix="张"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="识别对象"
                  value={analyses.reduce((sum, a) => sum + a.results.length, 0)}
                  prefix={<ScanOutlined />}
                  suffix="个"
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 分析结果 */}
      <Card
        title={
          <Space>
            <EyeOutlined />
            <span>分析结果</span>
            <Badge count={analyses.length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => message.info('导出功能开发中')}
            >
              导出
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={clearAll}
              disabled={analyses.length === 0}
            >
              清空
            </Button>
          </Space>
        }
        style={{ marginTop: 24 }}
      >
        {analyses.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <EyeOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>暂无分析结果</div>
            <div style={{ fontSize: 12 }}>上传图片或使用摄像头拍照来开始分析</div>
          </div>
        ) : (
          <Row gutter={16}>
            <Col span={8}>
              <List
                size="small"
                dataSource={analyses}
                renderItem={(item) => (
                  <List.Item
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedImage?.id === item.id ? '#f0f0f0' : 'transparent',
                      padding: '8px 12px',
                      borderRadius: 4,
                    }}
                    onClick={() => setSelectedImage(item)}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <Text strong style={{ fontSize: 12 }}>
                            {item.filename}
                          </Text>
                          <Badge count={item.results.length} size="small" />
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            {item.timestamp}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            处理时间: {item.processingTime.toFixed(1)}s
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Col>

            <Col span={16}>
              {selectedImage && (
                <div>
                  <div style={{ marginBottom: 16, textAlign: 'center' }}>
                    <Image
                      src={selectedImage.url}
                      alt={selectedImage.filename}
                      style={{ maxWidth: '100%', maxHeight: 400 }}
                      preview={{
                        mask: (
                          <Space>
                            <ZoomInOutlined />
                            预览
                          </Space>
                        ),
                      }}
                    />
                  </div>

                  <Divider orientation="left">识别结果</Divider>

                  <List
                    dataSource={selectedImage.results}
                    renderItem={(result) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={
                            <div style={{
                              width: 32,
                              height: 32,
                              borderRadius: '50%',
                              backgroundColor: getTypeColor(result.type),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                            }}>
                              {getTypeIcon(result.type)}
                            </div>
                          }
                          title={
                            <Space>
                              <Text strong>{result.label}</Text>
                              <Tag color={getTypeColor(result.type)}>
                                {recognitionTypes.find(t => t.value === result.type)?.label}
                              </Tag>
                              <Tag color={result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red'}>
                                {(result.confidence * 100).toFixed(1)}%
                              </Tag>
                            </Space>
                          }
                          description={
                            <div>
                              {result.bbox && (
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                  位置: ({result.bbox.x}, {result.bbox.y})
                                  尺寸: {result.bbox.width}×{result.bbox.height}
                                </Text>
                              )}
                              {result.attributes && (
                                <div style={{ marginTop: 4 }}>
                                  {Object.entries(result.attributes).map(([key, value]) => (
                                    <Tag key={key}>
                                      {key}: {value}
                                    </Tag>
                                  ))}
                                </div>
                              )}
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </Col>
          </Row>
        )}
      </Card>

      {/* 预览模态框 */}
      <Modal
        open={previewVisible}
        title="图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default VisionRecognition;
