{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Recognition\\\\DocumentRecognition.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Typo<PERSON>, Card, Button, Space, Row, Col, Upload, Image, List, Tag, Progress, Tabs, Select, Switch, Slider, message, Modal, Statistic, Badge, Table, Input, Collapse } from 'antd';\nimport { ArrowLeftOutlined, FileTextOutlined, UploadOutlined, CameraOutlined, DeleteOutlined, DownloadOutlined, ZoomInOutlined, ClearOutlined, ScanOutlined, IdcardOutlined, BankOutlined, SafetyCertificateOutlined, FileImageOutlined, FilePdfOutlined, CopyOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst {\n  Panel\n} = Collapse;\nconst DocumentRecognition = () => {\n  _s();\n  var _mockTableData$;\n  const navigate = useNavigate();\n  const [fileList, setFileList] = useState([]);\n  const [analyses, setAnalyses] = useState([]);\n  const [selectedDocument, setSelectedDocument] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [selectedTypes, setSelectedTypes] = useState(['text', 'table', 'fields']);\n  const [confidence, setConfidence] = useState(70);\n  const [enableStructure, setEnableStructure] = useState(true);\n  const [enableTable, setEnableTable] = useState(true);\n  const [language, setLanguage] = useState('zh-CN');\n  const cameraRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n\n  // 文档类型配置\n  const documentTypes = [{\n    value: 'id_card',\n    label: '身份证',\n    icon: /*#__PURE__*/_jsxDEV(IdcardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 45\n    }, this),\n    color: '#1890ff'\n  }, {\n    value: 'passport',\n    label: '护照',\n    icon: /*#__PURE__*/_jsxDEV(SafetyCertificateOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 45\n    }, this),\n    color: '#52c41a'\n  }, {\n    value: 'invoice',\n    label: '发票',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 44\n    }, this),\n    color: '#faad14'\n  }, {\n    value: 'contract',\n    label: '合同',\n    icon: /*#__PURE__*/_jsxDEV(FilePdfOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 45\n    }, this),\n    color: '#722ed1'\n  }, {\n    value: 'receipt',\n    label: '收据',\n    icon: /*#__PURE__*/_jsxDEV(FileImageOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 44\n    }, this),\n    color: '#eb2f96'\n  }, {\n    value: 'bank_card',\n    label: '银行卡',\n    icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 47\n    }, this),\n    color: '#13c2c2'\n  }, {\n    value: 'license',\n    label: '证照',\n    icon: /*#__PURE__*/_jsxDEV(SafetyCertificateOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 44\n    }, this),\n    color: '#fa8c16'\n  }, {\n    value: 'certificate',\n    label: '证书',\n    icon: /*#__PURE__*/_jsxDEV(SafetyCertificateOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 48\n    }, this),\n    color: '#a0d911'\n  }, {\n    value: 'general',\n    label: '通用文档',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 46\n    }, this),\n    color: '#666'\n  }];\n\n  // 语言选项\n  const languageOptions = [{\n    value: 'zh-CN',\n    label: '中文（简体）'\n  }, {\n    value: 'zh-TW',\n    label: '中文（繁体）'\n  }, {\n    value: 'en-US',\n    label: 'English'\n  }, {\n    value: 'ja-JP',\n    label: '日本語'\n  }, {\n    value: 'ko-KR',\n    label: '한국어'\n  }];\n\n  // 模拟OCR结果\n  const mockOCRResults = [{\n    id: '1',\n    text: '中华人民共和国居民身份证',\n    confidence: 0.98,\n    bbox: {\n      x: 50,\n      y: 20,\n      width: 300,\n      height: 25\n    },\n    type: 'text'\n  }, {\n    id: '2',\n    text: '张三',\n    confidence: 0.95,\n    bbox: {\n      x: 120,\n      y: 80,\n      width: 60,\n      height: 20\n    },\n    type: 'text'\n  }, {\n    id: '3',\n    text: '男',\n    confidence: 0.92,\n    bbox: {\n      x: 120,\n      y: 110,\n      width: 20,\n      height: 20\n    },\n    type: 'text'\n  }, {\n    id: '4',\n    text: '1990年01月01日',\n    confidence: 0.94,\n    bbox: {\n      x: 120,\n      y: 140,\n      width: 100,\n      height: 20\n    },\n    type: 'date'\n  }, {\n    id: '5',\n    text: '110101199001010001',\n    confidence: 0.96,\n    bbox: {\n      x: 120,\n      y: 170,\n      width: 150,\n      height: 20\n    },\n    type: 'number'\n  }];\n\n  // 模拟提取字段\n  const mockExtractedFields = {\n    name: '张三',\n    gender: '男',\n    birthDate: '1990年01月01日',\n    idNumber: '110101199001010001',\n    address: '北京市东城区某某街道某某号',\n    issueDate: '2020年01月01日',\n    expiryDate: '2030年01月01日',\n    issuingAuthority: '北京市公安局东城分局'\n  };\n\n  // 模拟表格数据\n  const mockTableData = [[{\n    row: 0,\n    col: 0,\n    text: '项目',\n    confidence: 0.95\n  }, {\n    row: 0,\n    col: 1,\n    text: '数量',\n    confidence: 0.93\n  }, {\n    row: 0,\n    col: 2,\n    text: '单价',\n    confidence: 0.94\n  }, {\n    row: 0,\n    col: 3,\n    text: '金额',\n    confidence: 0.96\n  }], [{\n    row: 1,\n    col: 0,\n    text: '办公用品',\n    confidence: 0.92\n  }, {\n    row: 1,\n    col: 1,\n    text: '10',\n    confidence: 0.98\n  }, {\n    row: 1,\n    col: 2,\n    text: '50.00',\n    confidence: 0.97\n  }, {\n    row: 1,\n    col: 3,\n    text: '500.00',\n    confidence: 0.95\n  }], [{\n    row: 2,\n    col: 0,\n    text: '电子设备',\n    confidence: 0.94\n  }, {\n    row: 2,\n    col: 1,\n    text: '2',\n    confidence: 0.99\n  }, {\n    row: 2,\n    col: 2,\n    text: '1200.00',\n    confidence: 0.96\n  }, {\n    row: 2,\n    col: 3,\n    text: '2400.00',\n    confidence: 0.98\n  }]];\n  const handleUpload = async options => {\n    const {\n      file,\n      onSuccess,\n      onError\n    } = options;\n    try {\n      setIsAnalyzing(true);\n\n      // 模拟文件上传和分析\n      await new Promise(resolve => setTimeout(resolve, 3000));\n      const imageUrl = URL.createObjectURL(file);\n      const newAnalysis = {\n        id: Date.now().toString(),\n        filename: file.name,\n        url: imageUrl,\n        size: file.size,\n        type: 'id_card',\n        // 模拟识别为身份证\n        ocrResults: mockOCRResults,\n        tableData: enableTable ? mockTableData : undefined,\n        extractedFields: mockExtractedFields,\n        timestamp: new Date().toLocaleString(),\n        processingTime: 2.5 + Math.random() * 2,\n        confidence: 0.85 + Math.random() * 0.15\n      };\n      setAnalyses(prev => [newAnalysis, ...prev]);\n      setSelectedDocument(newAnalysis);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(newAnalysis);\n      message.success(`${file.name} 识别完成`);\n    } catch (error) {\n      onError === null || onError === void 0 ? void 0 : onError(error);\n      message.error('识别失败，请重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n  const handlePreview = async file => {\n    if (!file.url && !file.preview) {\n      file.preview = await getBase64(file.originFileObj);\n    }\n    setPreviewImage(file.url || file.preview || '');\n    setPreviewVisible(true);\n  };\n  const getBase64 = file => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result);\n    reader.onerror = error => reject(error);\n  });\n  const startCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: 640,\n          height: 480\n        }\n      });\n      if (cameraRef.current) {\n        cameraRef.current.srcObject = stream;\n        setIsCameraActive(true);\n        message.success('摄像头已启动');\n      }\n    } catch (error) {\n      message.error('无法访问摄像头，请检查权限设置');\n    }\n  };\n  const stopCamera = () => {\n    var _cameraRef$current;\n    if ((_cameraRef$current = cameraRef.current) !== null && _cameraRef$current !== void 0 && _cameraRef$current.srcObject) {\n      const stream = cameraRef.current.srcObject;\n      stream.getTracks().forEach(track => track.stop());\n      cameraRef.current.srcObject = null;\n      setIsCameraActive(false);\n      message.info('摄像头已关闭');\n    }\n  };\n  const capturePhoto = () => {\n    if (cameraRef.current && canvasRef.current) {\n      const canvas = canvasRef.current;\n      const video = cameraRef.current;\n      const context = canvas.getContext('2d');\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      context === null || context === void 0 ? void 0 : context.drawImage(video, 0, 0);\n      canvas.toBlob(blob => {\n        if (blob) {\n          const file = new File([blob], `document_${Date.now()}.jpg`, {\n            type: 'image/jpeg'\n          });\n          handleUpload({\n            file,\n            onSuccess: () => {},\n            onError: () => {}\n          });\n        }\n      });\n    }\n  };\n  const clearAll = () => {\n    setAnalyses([]);\n    setSelectedDocument(null);\n    setFileList([]);\n    message.success('已清空所有识别结果');\n  };\n  const getTypeIcon = type => {\n    const typeConfig = documentTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.icon) || /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 32\n    }, this);\n  };\n  const getTypeColor = type => {\n    const typeConfig = documentTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.color) || '#1890ff';\n  };\n  const getTypeLabel = type => {\n    const typeConfig = documentTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.label) || '未知类型';\n  };\n\n  // 表格列定义\n  const tableColumns = ((_mockTableData$ = mockTableData[0]) === null || _mockTableData$ === void 0 ? void 0 : _mockTableData$.map((_, colIndex) => ({\n    title: `列 ${colIndex + 1}`,\n    dataIndex: colIndex,\n    key: colIndex,\n    render: (text, record, rowIndex) => {\n      var _mockTableData$rowInd;\n      const cell = (_mockTableData$rowInd = mockTableData[rowIndex]) === null || _mockTableData$rowInd === void 0 ? void 0 : _mockTableData$rowInd[colIndex];\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: cell === null || cell === void 0 ? void 0 : cell.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 11,\n            color: '#999'\n          },\n          children: [\"\\u7F6E\\u4FE1\\u5EA6: \", (((cell === null || cell === void 0 ? void 0 : cell.confidence) || 0) * 100).toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this);\n    }\n  }))) || [];\n  const tableDataSource = mockTableData.map((row, index) => ({\n    key: index,\n    ...row.reduce((acc, cell, colIndex) => {\n      acc[colIndex] = cell.text;\n      return acc;\n    }, {})\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(-1),\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u6587\\u6863\\u8BC6\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            defaultActiveKey: \"upload\",\n            children: [/*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n              tab: \"\\u6587\\u4EF6\\u4E0A\\u4F20\",\n              children: [/*#__PURE__*/_jsxDEV(Upload.Dragger, {\n                name: \"file\",\n                multiple: true,\n                accept: \"image/*,.pdf,.doc,.docx\",\n                customRequest: handleUpload,\n                onPreview: handlePreview,\n                fileList: fileList,\n                onChange: ({\n                  fileList\n                }) => setFileList(fileList),\n                disabled: isAnalyzing,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-drag-icon\",\n                  children: /*#__PURE__*/_jsxDEV(UploadOutlined, {\n                    style: {\n                      fontSize: 48,\n                      color: '#1890ff'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-text\",\n                  children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u6863\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-hint\",\n                  children: \"\\u652F\\u6301 JPG\\u3001PNG\\u3001PDF\\u3001DOC\\u3001DOCX \\u7B49\\u683C\\u5F0F\\uFF0C\\u5355\\u4E2A\\u6587\\u4EF6\\u4E0D\\u8D85\\u8FC7 20MB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Progress, {\n                  type: \"circle\",\n                  percent: 75\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u6B63\\u5728\\u8BC6\\u522B\\u6587\\u6863\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this)]\n            }, \"upload\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n              tab: \"\\u6444\\u50CF\\u5934\\u62CD\\u7167\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 16\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: cameraRef,\n                    autoPlay: true,\n                    playsInline: true,\n                    style: {\n                      width: '100%',\n                      maxWidth: 640,\n                      height: 'auto',\n                      border: '1px solid #d9d9d9',\n                      borderRadius: 6,\n                      display: isCameraActive ? 'block' : 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                      display: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 21\n                  }, this), !isCameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100%',\n                      height: 300,\n                      border: '1px dashed #d9d9d9',\n                      borderRadius: 6,\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexDirection: 'column',\n                      color: '#999'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(CameraOutlined, {\n                      style: {\n                        fontSize: 48,\n                        marginBottom: 16\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 437,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u70B9\\u51FB\\u4E0B\\u65B9\\u6309\\u94AE\\u542F\\u52A8\\u6444\\u50CF\\u5934\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Space, {\n                  children: !isCameraActive ? /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 31\n                    }, this),\n                    onClick: startCamera,\n                    children: \"\\u542F\\u52A8\\u6444\\u50CF\\u5934\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 33\n                      }, this),\n                      onClick: capturePhoto,\n                      loading: isAnalyzing,\n                      children: \"\\u62CD\\u7167\\u8BC6\\u522B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 463,\n                        columnNumber: 33\n                      }, this),\n                      onClick: stopCamera,\n                      children: \"\\u5173\\u95ED\\u6444\\u50CF\\u5934\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)\n            }, \"camera\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BC6\\u522B\\u8BBE\\u7F6E\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u8BC6\\u522B\\u8BED\\u8A00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: language,\n                onChange: setLanguage,\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                children: languageOptions.map(lang => /*#__PURE__*/_jsxDEV(Option, {\n                  value: lang.value,\n                  children: lang.label\n                }, lang.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u7F6E\\u4FE1\\u5EA6\\u9608\\u503C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: confidence,\n                onChange: setConfidence,\n                style: {\n                  marginTop: 8\n                },\n                tooltip: {\n                  formatter: value => `${value}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: 12\n                },\n                children: [\"\\u5F53\\u524D: \", confidence, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                style: {\n                  width: '100%',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u7ED3\\u6784\\u5316\\u8BC6\\u522B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: enableStructure,\n                  onChange: setEnableStructure\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                style: {\n                  width: '100%',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8868\\u683C\\u8BC6\\u522B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: enableTable,\n                  onChange: setEnableTable\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7EDF\\u8BA1\\u4FE1\\u606F\",\n          size: \"small\",\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5DF2\\u8BC6\\u522B\",\n                value: analyses.length,\n                prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 27\n                }, this),\n                suffix: \"\\u4EFD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5E73\\u5747\\u51C6\\u786E\\u7387\",\n                value: analyses.length > 0 ? (analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length * 100).toFixed(1) : 0,\n                suffix: \"%\",\n                prefix: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u8BC6\\u522B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n          count: analyses.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 21\n          }, this),\n          onClick: () => message.info('导出功能开发中'),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 21\n          }, this),\n          onClick: clearAll,\n          disabled: analyses.length === 0,\n          children: \"\\u6E05\\u7A7A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 11\n      }, this),\n      style: {\n        marginTop: 24\n      },\n      children: analyses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0',\n          color: '#999'\n        },\n        children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {\n          style: {\n            fontSize: 48,\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u6682\\u65E0\\u8BC6\\u522B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12\n          },\n          children: \"\\u4E0A\\u4F20\\u6587\\u6863\\u6216\\u4F7F\\u7528\\u6444\\u50CF\\u5934\\u62CD\\u7167\\u6765\\u5F00\\u59CB\\u8BC6\\u522B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: analyses,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                cursor: 'pointer',\n                backgroundColor: (selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.id) === item.id ? '#f0f0f0' : 'transparent',\n                padding: '8px 12px',\n                borderRadius: 4\n              },\n              onClick: () => setSelectedDocument(item),\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: 32,\n                    height: 32,\n                    borderRadius: '50%',\n                    backgroundColor: getTypeColor(item.type),\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white'\n                  },\n                  children: getTypeIcon(item.type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 25\n                }, this),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      fontSize: 12\n                    },\n                    children: item.filename\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: getTypeColor(item.type),\n                    children: getTypeLabel(item.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 25\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 11\n                    },\n                    children: item.timestamp\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 11\n                    },\n                    children: [\"\\u51C6\\u786E\\u7387: \", (item.confidence * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 16,\n          children: selectedDocument && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16,\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Image, {\n                src: selectedDocument.url,\n                alt: selectedDocument.filename,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 300\n                },\n                preview: {\n                  mask: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(ZoomInOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 29\n                    }, this), \"\\u9884\\u89C8\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 27\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n              defaultActiveKey: \"ocr\",\n              children: [/*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n                tab: \"OCR\\u6587\\u5B57\",\n                children: /*#__PURE__*/_jsxDEV(List, {\n                  size: \"small\",\n                  dataSource: selectedDocument.ocrResults,\n                  renderItem: result => /*#__PURE__*/_jsxDEV(List.Item, {\n                    actions: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"text\",\n                      size: \"small\",\n                      icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 39\n                      }, this),\n                      onClick: () => {\n                        navigator.clipboard.writeText(result.text);\n                        message.success('已复制到剪贴板');\n                      }\n                    }, \"copy\", false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 31\n                    }, this)],\n                    children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                      title: /*#__PURE__*/_jsxDEV(Space, {\n                        children: [/*#__PURE__*/_jsxDEV(Text, {\n                          children: result.text\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 690,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                          color: result.confidence >= 0.9 ? 'green' : result.confidence >= 0.7 ? 'orange' : 'red',\n                          children: [(result.confidence * 100).toFixed(1), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 691,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                          color: \"blue\",\n                          children: result.type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 694,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 33\n                      }, this),\n                      description: /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        style: {\n                          fontSize: 12\n                        },\n                        children: [\"\\u4F4D\\u7F6E: (\", result.bbox.x, \", \", result.bbox.y, \") \\u5C3A\\u5BF8: \", result.bbox.width, \"\\xD7\", result.bbox.height]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 23\n                }, this)\n              }, \"ocr\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 21\n              }, this), selectedDocument.extractedFields && /*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n                tab: \"\\u7ED3\\u6784\\u5316\\u5B57\\u6BB5\",\n                children: /*#__PURE__*/_jsxDEV(Collapse, {\n                  defaultActiveKey: ['1'],\n                  children: /*#__PURE__*/_jsxDEV(Panel, {\n                    header: \"\\u63D0\\u53D6\\u5B57\\u6BB5\",\n                    children: /*#__PURE__*/_jsxDEV(Row, {\n                      gutter: [16, 16],\n                      children: Object.entries(selectedDocument.extractedFields).map(([key, value]) => /*#__PURE__*/_jsxDEV(Col, {\n                        span: 12,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            padding: 12,\n                            border: '1px solid #d9d9d9',\n                            borderRadius: 4,\n                            backgroundColor: '#fafafa'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontSize: 12,\n                              color: '#666',\n                              marginBottom: 4\n                            },\n                            children: key\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 722,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontWeight: 500\n                            },\n                            children: value\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 725,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 716,\n                          columnNumber: 35\n                        }, this)\n                      }, key, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 29\n                    }, this)\n                  }, \"1\", false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 25\n                }, this)\n              }, \"fields\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 23\n              }, this), selectedDocument.tableData && /*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n                tab: \"\\u8868\\u683C\\u6570\\u636E\",\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  columns: tableColumns,\n                  dataSource: tableDataSource,\n                  size: \"small\",\n                  pagination: false,\n                  bordered: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 25\n                }, this)\n              }, \"table\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: previewVisible,\n      title: \"\\u6587\\u6863\\u9884\\u89C8\",\n      footer: null,\n      onCancel: () => setPreviewVisible(false),\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        alt: \"preview\",\n        style: {\n          width: '100%'\n        },\n        src: previewImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentRecognition, \"yZU82/FhvmPRobJ4OCw0/oWhw3c=\", false, function () {\n  return [useNavigate];\n});\n_c = DocumentRecognition;\nexport default DocumentRecognition;\nvar _c;\n$RefreshReg$(_c, \"DocumentRecognition\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Typography", "Card", "<PERSON><PERSON>", "Space", "Row", "Col", "Upload", "Image", "List", "Tag", "Progress", "Tabs", "Select", "Switch", "Slide<PERSON>", "message", "Modal", "Statistic", "Badge", "Table", "Input", "Collapse", "ArrowLeftOutlined", "FileTextOutlined", "UploadOutlined", "CameraOutlined", "DeleteOutlined", "DownloadOutlined", "ZoomInOutlined", "ClearOutlined", "ScanOutlined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BankOutlined", "SafetyCertificateOutlined", "FileImageOutlined", "FilePdfOutlined", "CopyOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Paragraph", "Text", "Option", "TextArea", "Panel", "DocumentRecognition", "_s", "_mockTableData$", "navigate", "fileList", "setFileList", "analyses", "setAnalyses", "selectedDocument", "setSelectedDocument", "isAnalyzing", "setIsAnalyzing", "previewVisible", "setPreviewVisible", "previewImage", "setPreviewImage", "selectedTypes", "setSelectedTypes", "confidence", "setConfidence", "enableStructure", "setEnableStructure", "enableTable", "setEnableTable", "language", "setLanguage", "cameraRef", "canvasRef", "isCameraActive", "setIsCameraActive", "documentTypes", "value", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "languageOptions", "mockOCRResults", "id", "text", "bbox", "x", "y", "width", "height", "type", "mockExtractedFields", "name", "gender", "birthDate", "idNumber", "address", "issueDate", "expiryDate", "issuingAuthority", "mockTableData", "row", "col", "handleUpload", "options", "file", "onSuccess", "onError", "Promise", "resolve", "setTimeout", "imageUrl", "URL", "createObjectURL", "newAnalysis", "Date", "now", "toString", "filename", "url", "size", "ocrResults", "tableData", "undefined", "<PERSON><PERSON>ields", "timestamp", "toLocaleString", "processingTime", "Math", "random", "prev", "success", "error", "handlePreview", "preview", "getBase64", "originFileObj", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "startCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "current", "srcObject", "stopCamera", "_cameraRef$current", "getTracks", "for<PERSON>ach", "track", "stop", "info", "capturePhoto", "canvas", "context", "getContext", "videoWidth", "videoHeight", "drawImage", "toBlob", "blob", "File", "clearAll", "getTypeIcon", "typeConfig", "find", "t", "getTypeColor", "getTypeLabel", "tableColumns", "map", "_", "colIndex", "title", "dataIndex", "key", "render", "record", "rowIndex", "_mockTableData$rowInd", "cell", "children", "style", "fontSize", "toFixed", "tableDataSource", "index", "reduce", "acc", "marginBottom", "onClick", "gutter", "span", "defaultActiveKey", "TabPane", "tab", "<PERSON><PERSON>", "multiple", "accept", "customRequest", "onPreview", "onChange", "disabled", "className", "marginTop", "textAlign", "percent", "ref", "autoPlay", "playsInline", "max<PERSON><PERSON><PERSON>", "border", "borderRadius", "display", "alignItems", "justifyContent", "flexDirection", "loading", "direction", "strong", "lang", "tooltip", "formatter", "checked", "length", "prefix", "suffix", "sum", "a", "count", "extra", "padding", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "cursor", "backgroundColor", "Meta", "avatar", "description", "src", "alt", "maxHeight", "mask", "actions", "clipboard", "writeText", "header", "Object", "entries", "fontWeight", "columns", "pagination", "bordered", "open", "footer", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Recognition/DocumentRecognition.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport {\n  Typo<PERSON>,\n  Card,\n  Button,\n  Space,\n  Row,\n  Col,\n  Upload,\n  Image,\n  List,\n  Tag,\n  Progress,\n  Alert,\n  Tabs,\n  Select,\n  Switch,\n  Slider,\n  message,\n  Modal,\n  Divider,\n  Statistic,\n  Badge,\n  Table,\n  Input,\n  Collapse,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  FileTextOutlined,\n  UploadOutlined,\n  CameraOutlined,\n  DeleteOutlined,\n  DownloadOutlined,\n  ZoomInOutlined,\n  ClearOutlined,\n  ScanOutlined,\n  PictureOutlined,\n  TableOutlined,\n  IdcardOutlined,\n  BankOutlined,\n  SafetyCertificateOutlined,\n  FileImageOutlined,\n  FilePdfOutlined,\n  FileWordOutlined,\n  FileExcelOutlined,\n  SearchOutlined,\n  CopyOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport type { UploadFile, UploadProps } from 'antd/es/upload/interface';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Paragraph, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\nconst { Panel } = Collapse;\n\ninterface OCRResult {\n  id: string;\n  text: string;\n  confidence: number;\n  bbox: {\n    x: number;\n    y: number;\n    width: number;\n    height: number;\n  };\n  type: 'text' | 'number' | 'date' | 'signature' | 'stamp';\n}\n\ninterface TableCell {\n  row: number;\n  col: number;\n  text: string;\n  confidence: number;\n}\n\ninterface DocumentAnalysis {\n  id: string;\n  filename: string;\n  url: string;\n  size: number;\n  type: 'id_card' | 'passport' | 'invoice' | 'contract' | 'receipt' | 'bank_card' | 'license' | 'certificate' | 'general';\n  ocrResults: OCRResult[];\n  tableData?: TableCell[][];\n  extractedFields?: Record<string, any>;\n  timestamp: string;\n  processingTime: number;\n  confidence: number;\n}\n\nconst DocumentRecognition: React.FC = () => {\n  const navigate = useNavigate();\n  const [fileList, setFileList] = useState<UploadFile[]>([]);\n  const [analyses, setAnalyses] = useState<DocumentAnalysis[]>([]);\n  const [selectedDocument, setSelectedDocument] = useState<DocumentAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [selectedTypes, setSelectedTypes] = useState<string[]>(['text', 'table', 'fields']);\n  const [confidence, setConfidence] = useState(70);\n  const [enableStructure, setEnableStructure] = useState(true);\n  const [enableTable, setEnableTable] = useState(true);\n  const [language, setLanguage] = useState('zh-CN');\n\n  const cameraRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n\n  // 文档类型配置\n  const documentTypes = [\n    { value: 'id_card', label: '身份证', icon: <IdcardOutlined />, color: '#1890ff' },\n    { value: 'passport', label: '护照', icon: <SafetyCertificateOutlined />, color: '#52c41a' },\n    { value: 'invoice', label: '发票', icon: <FileTextOutlined />, color: '#faad14' },\n    { value: 'contract', label: '合同', icon: <FilePdfOutlined />, color: '#722ed1' },\n    { value: 'receipt', label: '收据', icon: <FileImageOutlined />, color: '#eb2f96' },\n    { value: 'bank_card', label: '银行卡', icon: <BankOutlined />, color: '#13c2c2' },\n    { value: 'license', label: '证照', icon: <SafetyCertificateOutlined />, color: '#fa8c16' },\n    { value: 'certificate', label: '证书', icon: <SafetyCertificateOutlined />, color: '#a0d911' },\n    { value: 'general', label: '通用文档', icon: <FileTextOutlined />, color: '#666' },\n  ];\n\n  // 语言选项\n  const languageOptions = [\n    { value: 'zh-CN', label: '中文（简体）' },\n    { value: 'zh-TW', label: '中文（繁体）' },\n    { value: 'en-US', label: 'English' },\n    { value: 'ja-JP', label: '日本語' },\n    { value: 'ko-KR', label: '한국어' },\n  ];\n\n  // 模拟OCR结果\n  const mockOCRResults: OCRResult[] = [\n    {\n      id: '1',\n      text: '中华人民共和国居民身份证',\n      confidence: 0.98,\n      bbox: { x: 50, y: 20, width: 300, height: 25 },\n      type: 'text',\n    },\n    {\n      id: '2',\n      text: '张三',\n      confidence: 0.95,\n      bbox: { x: 120, y: 80, width: 60, height: 20 },\n      type: 'text',\n    },\n    {\n      id: '3',\n      text: '男',\n      confidence: 0.92,\n      bbox: { x: 120, y: 110, width: 20, height: 20 },\n      type: 'text',\n    },\n    {\n      id: '4',\n      text: '1990年01月01日',\n      confidence: 0.94,\n      bbox: { x: 120, y: 140, width: 100, height: 20 },\n      type: 'date',\n    },\n    {\n      id: '5',\n      text: '110101199001010001',\n      confidence: 0.96,\n      bbox: { x: 120, y: 170, width: 150, height: 20 },\n      type: 'number',\n    },\n  ];\n\n  // 模拟提取字段\n  const mockExtractedFields = {\n    name: '张三',\n    gender: '男',\n    birthDate: '1990年01月01日',\n    idNumber: '110101199001010001',\n    address: '北京市东城区某某街道某某号',\n    issueDate: '2020年01月01日',\n    expiryDate: '2030年01月01日',\n    issuingAuthority: '北京市公安局东城分局',\n  };\n\n  // 模拟表格数据\n  const mockTableData: TableCell[][] = [\n    [\n      { row: 0, col: 0, text: '项目', confidence: 0.95 },\n      { row: 0, col: 1, text: '数量', confidence: 0.93 },\n      { row: 0, col: 2, text: '单价', confidence: 0.94 },\n      { row: 0, col: 3, text: '金额', confidence: 0.96 },\n    ],\n    [\n      { row: 1, col: 0, text: '办公用品', confidence: 0.92 },\n      { row: 1, col: 1, text: '10', confidence: 0.98 },\n      { row: 1, col: 2, text: '50.00', confidence: 0.97 },\n      { row: 1, col: 3, text: '500.00', confidence: 0.95 },\n    ],\n    [\n      { row: 2, col: 0, text: '电子设备', confidence: 0.94 },\n      { row: 2, col: 1, text: '2', confidence: 0.99 },\n      { row: 2, col: 2, text: '1200.00', confidence: 0.96 },\n      { row: 2, col: 3, text: '2400.00', confidence: 0.98 },\n    ],\n  ];\n\n  const handleUpload: UploadProps['customRequest'] = async (options) => {\n    const { file, onSuccess, onError } = options;\n\n    try {\n      setIsAnalyzing(true);\n\n      // 模拟文件上传和分析\n      await new Promise(resolve => setTimeout(resolve, 3000));\n\n      const imageUrl = URL.createObjectURL(file as File);\n      const newAnalysis: DocumentAnalysis = {\n        id: Date.now().toString(),\n        filename: (file as File).name,\n        url: imageUrl,\n        size: (file as File).size,\n        type: 'id_card', // 模拟识别为身份证\n        ocrResults: mockOCRResults,\n        tableData: enableTable ? mockTableData : undefined,\n        extractedFields: mockExtractedFields,\n        timestamp: new Date().toLocaleString(),\n        processingTime: 2.5 + Math.random() * 2,\n        confidence: 0.85 + Math.random() * 0.15,\n      };\n\n      setAnalyses(prev => [newAnalysis, ...prev]);\n      setSelectedDocument(newAnalysis);\n\n      onSuccess?.(newAnalysis);\n      message.success(`${(file as File).name} 识别完成`);\n    } catch (error) {\n      onError?.(error as Error);\n      message.error('识别失败，请重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const handlePreview = async (file: UploadFile) => {\n    if (!file.url && !file.preview) {\n      file.preview = await getBase64(file.originFileObj as File);\n    }\n    setPreviewImage(file.url || file.preview || '');\n    setPreviewVisible(true);\n  };\n\n  const getBase64 = (file: File): Promise<string> =>\n    new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result as string);\n      reader.onerror = error => reject(error);\n    });\n\n  const startCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { width: 640, height: 480 }\n      });\n      if (cameraRef.current) {\n        cameraRef.current.srcObject = stream;\n        setIsCameraActive(true);\n        message.success('摄像头已启动');\n      }\n    } catch (error) {\n      message.error('无法访问摄像头，请检查权限设置');\n    }\n  };\n\n  const stopCamera = () => {\n    if (cameraRef.current?.srcObject) {\n      const stream = cameraRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      cameraRef.current.srcObject = null;\n      setIsCameraActive(false);\n      message.info('摄像头已关闭');\n    }\n  };\n\n  const capturePhoto = () => {\n    if (cameraRef.current && canvasRef.current) {\n      const canvas = canvasRef.current;\n      const video = cameraRef.current;\n      const context = canvas.getContext('2d');\n\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      context?.drawImage(video, 0, 0);\n\n      canvas.toBlob(blob => {\n        if (blob) {\n          const file = new File([blob], `document_${Date.now()}.jpg`, { type: 'image/jpeg' });\n          handleUpload({\n            file,\n            onSuccess: () => {},\n            onError: () => {},\n          } as any);\n        }\n      });\n    }\n  };\n\n  const clearAll = () => {\n    setAnalyses([]);\n    setSelectedDocument(null);\n    setFileList([]);\n    message.success('已清空所有识别结果');\n  };\n\n  const getTypeIcon = (type: string) => {\n    const typeConfig = documentTypes.find(t => t.value === type);\n    return typeConfig?.icon || <FileTextOutlined />;\n  };\n\n  const getTypeColor = (type: string) => {\n    const typeConfig = documentTypes.find(t => t.value === type);\n    return typeConfig?.color || '#1890ff';\n  };\n\n  const getTypeLabel = (type: string) => {\n    const typeConfig = documentTypes.find(t => t.value === type);\n    return typeConfig?.label || '未知类型';\n  };\n\n  // 表格列定义\n  const tableColumns: ColumnsType<any> = mockTableData[0]?.map((_, colIndex) => ({\n    title: `列 ${colIndex + 1}`,\n    dataIndex: colIndex,\n    key: colIndex,\n    render: (text: string, record: any, rowIndex: number) => {\n      const cell = mockTableData[rowIndex]?.[colIndex];\n      return (\n        <div>\n          <div>{cell?.text}</div>\n          <div style={{ fontSize: 11, color: '#999' }}>\n            置信度: {((cell?.confidence || 0) * 100).toFixed(1)}%\n          </div>\n        </div>\n      );\n    },\n  })) || [];\n\n  const tableDataSource = mockTableData.map((row, index) => ({\n    key: index,\n    ...row.reduce((acc, cell, colIndex) => {\n      acc[colIndex] = cell.text;\n      return acc;\n    }, {} as Record<number, string>),\n  }));\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(-1)}\n          >\n            返回\n          </Button>\n        </Space>\n      </div>\n\n      <Row gutter={24}>\n        <Col span={16}>\n          <Card title={\n            <Space>\n              <FileTextOutlined />\n              <span>文档识别</span>\n            </Space>\n          }>\n            <Tabs defaultActiveKey=\"upload\">\n              <Tabs.TabPane tab=\"文件上传\" key=\"upload\">\n                <Upload.Dragger\n                  name=\"file\"\n                  multiple\n                  accept=\"image/*,.pdf,.doc,.docx\"\n                  customRequest={handleUpload}\n                  onPreview={handlePreview}\n                  fileList={fileList}\n                  onChange={({ fileList }) => setFileList(fileList)}\n                  disabled={isAnalyzing}\n                >\n                  <p className=\"ant-upload-drag-icon\">\n                    <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n                  </p>\n                  <p className=\"ant-upload-text\">点击或拖拽文档到此区域上传</p>\n                  <p className=\"ant-upload-hint\">\n                    支持 JPG、PNG、PDF、DOC、DOCX 等格式，单个文件不超过 20MB\n                  </p>\n                </Upload.Dragger>\n\n                {isAnalyzing && (\n                  <div style={{ marginTop: 16, textAlign: 'center' }}>\n                    <Progress type=\"circle\" percent={75} />\n                    <div style={{ marginTop: 8 }}>\n                      <Text>正在识别文档，请稍候...</Text>\n                    </div>\n                  </div>\n                )}\n              </Tabs.TabPane>\n\n              <Tabs.TabPane tab=\"摄像头拍照\" key=\"camera\">\n                <div style={{ textAlign: 'center' }}>\n                  <div style={{ marginBottom: 16 }}>\n                    <video\n                      ref={cameraRef}\n                      autoPlay\n                      playsInline\n                      style={{\n                        width: '100%',\n                        maxWidth: 640,\n                        height: 'auto',\n                        border: '1px solid #d9d9d9',\n                        borderRadius: 6,\n                        display: isCameraActive ? 'block' : 'none',\n                      }}\n                    />\n                    <canvas ref={canvasRef} style={{ display: 'none' }} />\n\n                    {!isCameraActive && (\n                      <div style={{\n                        width: '100%',\n                        height: 300,\n                        border: '1px dashed #d9d9d9',\n                        borderRadius: 6,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        flexDirection: 'column',\n                        color: '#999',\n                      }}>\n                        <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />\n                        <Text type=\"secondary\">点击下方按钮启动摄像头</Text>\n                      </div>\n                    )}\n                  </div>\n\n                  <Space>\n                    {!isCameraActive ? (\n                      <Button\n                        type=\"primary\"\n                        icon={<CameraOutlined />}\n                        onClick={startCamera}\n                      >\n                        启动摄像头\n                      </Button>\n                    ) : (\n                      <>\n                        <Button\n                          type=\"primary\"\n                          icon={<CameraOutlined />}\n                          onClick={capturePhoto}\n                          loading={isAnalyzing}\n                        >\n                          拍照识别\n                        </Button>\n                        <Button\n                          icon={<DeleteOutlined />}\n                          onClick={stopCamera}\n                        >\n                          关闭摄像头\n                        </Button>\n                      </>\n                    )}\n                  </Space>\n                </div>\n              </Tabs.TabPane>\n            </Tabs>\n          </Card>\n        </Col>\n\n        <Col span={8}>\n          <Card title=\"识别设置\" size=\"small\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>识别语言</Text>\n                <Select\n                  value={language}\n                  onChange={setLanguage}\n                  style={{ width: '100%', marginTop: 8 }}\n                >\n                  {languageOptions.map(lang => (\n                    <Option key={lang.value} value={lang.value}>\n                      {lang.label}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              <div>\n                <Text strong>置信度阈值</Text>\n                <Slider\n                  value={confidence}\n                  onChange={setConfidence}\n                  style={{ marginTop: 8 }}\n                  tooltip={{ formatter: (value) => `${value}%` }}\n                />\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                  当前: {confidence}%\n                </Text>\n              </div>\n\n              <div>\n                <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n                  <Text strong>结构化识别</Text>\n                  <Switch\n                    checked={enableStructure}\n                    onChange={setEnableStructure}\n                  />\n                </Space>\n              </div>\n\n              <div>\n                <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n                  <Text strong>表格识别</Text>\n                  <Switch\n                    checked={enableTable}\n                    onChange={setEnableTable}\n                  />\n                </Space>\n              </div>\n            </Space>\n          </Card>\n\n          <Card title=\"统计信息\" size=\"small\" style={{ marginTop: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic\n                  title=\"已识别\"\n                  value={analyses.length}\n                  prefix={<FileTextOutlined />}\n                  suffix=\"份\"\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"平均准确率\"\n                  value={analyses.length > 0 ?\n                    (analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length * 100).toFixed(1) :\n                    0\n                  }\n                  suffix=\"%\"\n                  prefix={<ScanOutlined />}\n                />\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 识别结果 */}\n      <Card\n        title={\n          <Space>\n            <FileTextOutlined />\n            <span>识别结果</span>\n            <Badge count={analyses.length} />\n          </Space>\n        }\n        extra={\n          <Space>\n            <Button\n              icon={<DownloadOutlined />}\n              onClick={() => message.info('导出功能开发中')}\n            >\n              导出\n            </Button>\n            <Button\n              icon={<ClearOutlined />}\n              onClick={clearAll}\n              disabled={analyses.length === 0}\n            >\n              清空\n            </Button>\n          </Space>\n        }\n        style={{ marginTop: 24 }}\n      >\n        {analyses.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>\n            <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />\n            <div>暂无识别结果</div>\n            <div style={{ fontSize: 12 }}>上传文档或使用摄像头拍照来开始识别</div>\n          </div>\n        ) : (\n          <Row gutter={16}>\n            <Col span={8}>\n              <List\n                size=\"small\"\n                dataSource={analyses}\n                renderItem={(item) => (\n                  <List.Item\n                    style={{\n                      cursor: 'pointer',\n                      backgroundColor: selectedDocument?.id === item.id ? '#f0f0f0' : 'transparent',\n                      padding: '8px 12px',\n                      borderRadius: 4,\n                    }}\n                    onClick={() => setSelectedDocument(item)}\n                  >\n                    <List.Item.Meta\n                      avatar={\n                        <div style={{\n                          width: 32,\n                          height: 32,\n                          borderRadius: '50%',\n                          backgroundColor: getTypeColor(item.type),\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: 'white',\n                        }}>\n                          {getTypeIcon(item.type)}\n                        </div>\n                      }\n                      title={\n                        <Space>\n                          <Text strong style={{ fontSize: 12 }}>\n                            {item.filename}\n                          </Text>\n                          <Tag color={getTypeColor(item.type)}>\n                            {getTypeLabel(item.type)}\n                          </Tag>\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                            {item.timestamp}\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                            准确率: {(item.confidence * 100).toFixed(1)}%\n                          </Text>\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Col>\n\n            <Col span={16}>\n              {selectedDocument && (\n                <div>\n                  <div style={{ marginBottom: 16, textAlign: 'center' }}>\n                    <Image\n                      src={selectedDocument.url}\n                      alt={selectedDocument.filename}\n                      style={{ maxWidth: '100%', maxHeight: 300 }}\n                      preview={{\n                        mask: (\n                          <Space>\n                            <ZoomInOutlined />\n                            预览\n                          </Space>\n                        ),\n                      }}\n                    />\n                  </div>\n\n                  <Tabs defaultActiveKey=\"ocr\">\n                    <Tabs.TabPane tab=\"OCR文字\" key=\"ocr\">\n                      <List\n                        size=\"small\"\n                        dataSource={selectedDocument.ocrResults}\n                        renderItem={(result) => (\n                          <List.Item\n                            actions={[\n                              <Button\n                                key=\"copy\"\n                                type=\"text\"\n                                size=\"small\"\n                                icon={<CopyOutlined />}\n                                onClick={() => {\n                                  navigator.clipboard.writeText(result.text);\n                                  message.success('已复制到剪贴板');\n                                }}\n                              />\n                            ]}\n                          >\n                            <List.Item.Meta\n                              title={\n                                <Space>\n                                  <Text>{result.text}</Text>\n                                  <Tag color={result.confidence >= 0.9 ? 'green' : result.confidence >= 0.7 ? 'orange' : 'red'}>\n                                    {(result.confidence * 100).toFixed(1)}%\n                                  </Tag>\n                                  <Tag color=\"blue\">{result.type}</Tag>\n                                </Space>\n                              }\n                              description={\n                                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                                  位置: ({result.bbox.x}, {result.bbox.y})\n                                  尺寸: {result.bbox.width}×{result.bbox.height}\n                                </Text>\n                              }\n                            />\n                          </List.Item>\n                        )}\n                      />\n                    </Tabs.TabPane>\n\n                    {selectedDocument.extractedFields && (\n                      <Tabs.TabPane tab=\"结构化字段\" key=\"fields\">\n                        <Collapse defaultActiveKey={['1']}>\n                          <Panel header=\"提取字段\" key=\"1\">\n                            <Row gutter={[16, 16]}>\n                              {Object.entries(selectedDocument.extractedFields).map(([key, value]) => (\n                                <Col span={12} key={key}>\n                                  <div style={{\n                                    padding: 12,\n                                    border: '1px solid #d9d9d9',\n                                    borderRadius: 4,\n                                    backgroundColor: '#fafafa',\n                                  }}>\n                                    <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>\n                                      {key}\n                                    </div>\n                                    <div style={{ fontWeight: 500 }}>\n                                      {value}\n                                    </div>\n                                  </div>\n                                </Col>\n                              ))}\n                            </Row>\n                          </Panel>\n                        </Collapse>\n                      </Tabs.TabPane>\n                    )}\n\n                    {selectedDocument.tableData && (\n                      <Tabs.TabPane tab=\"表格数据\" key=\"table\">\n                        <Table\n                          columns={tableColumns}\n                          dataSource={tableDataSource}\n                          size=\"small\"\n                          pagination={false}\n                          bordered\n                        />\n                      </Tabs.TabPane>\n                    )}\n                  </Tabs>\n                </div>\n              )}\n            </Col>\n          </Row>\n        )}\n      </Card>\n\n      {/* 预览模态框 */}\n      <Modal\n        open={previewVisible}\n        title=\"文档预览\"\n        footer={null}\n        onCancel={() => setPreviewVisible(false)}\n        width={800}\n      >\n        <img alt=\"preview\" style={{ width: '100%' }} src={previewImage} />\n      </Modal>\n    </div>\n  );\n};\n\nexport default DocumentRecognition;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SACEC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,QAAQ,EAERC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EAELC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,QAAQ,QACH,MAAM;AACb,SACEC,iBAAiB,EACjBC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,aAAa,EACbC,YAAY,EAGZC,cAAc,EACdC,YAAY,EACZC,yBAAyB,EACzBC,iBAAiB,EACjBC,eAAe,EAIfC,YAAY,QACP,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI/C,MAAM;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAG5C,UAAU;AAC7C,MAAM;EAAE6C;AAAO,CAAC,GAAGjC,MAAM;AACzB,MAAM;EAAEkC;AAAS,CAAC,GAAG1B,KAAK;AAC1B,MAAM;EAAE2B;AAAM,CAAC,GAAG1B,QAAQ;AAoC1B,MAAM2B,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC1C,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAM,CAACwD,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,QAAQ,CAAqB,EAAE,CAAC;EAChE,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAW,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;EACzF,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC,OAAO,CAAC;EAEjD,MAAM4E,SAAS,GAAG3E,MAAM,CAAmB,IAAI,CAAC;EAChD,MAAM4E,SAAS,GAAG5E,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMgF,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,eAAE1C,OAAA,CAACR,cAAc;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9E;IAAEP,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,eAAE1C,OAAA,CAACN,yBAAyB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzF;IAAEP,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,eAAE1C,OAAA,CAAChB,gBAAgB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/E;IAAEP,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,eAAE1C,OAAA,CAACJ,eAAe;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/E;IAAEP,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,eAAE1C,OAAA,CAACL,iBAAiB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAChF;IAAEP,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,eAAE1C,OAAA,CAACP,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9E;IAAEP,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,eAAE1C,OAAA,CAACN,yBAAyB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EACxF;IAAEP,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,eAAE1C,OAAA,CAACN,yBAAyB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5F;IAAEP,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAE1C,OAAA,CAAChB,gBAAgB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,CAC/E;;EAED;EACA,MAAMC,eAAe,GAAG,CACtB;IAAER,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACpC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAM,CAAC,EAChC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAM,CAAC,CACjC;;EAED;EACA,MAAMQ,cAA2B,GAAG,CAClC;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,cAAc;IACpBxB,UAAU,EAAE,IAAI;IAChByB,IAAI,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAC;IAC9CC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,IAAI;IACVxB,UAAU,EAAE,IAAI;IAChByB,IAAI,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC;IAC9CC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,GAAG;IACTxB,UAAU,EAAE,IAAI;IAChByB,IAAI,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC;IAC/CC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,aAAa;IACnBxB,UAAU,EAAE,IAAI;IAChByB,IAAI,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAC;IAChDC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,oBAAoB;IAC1BxB,UAAU,EAAE,IAAI;IAChByB,IAAI,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAC;IAChDC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,mBAAmB,GAAG;IAC1BC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,oBAAoB;IAC9BC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE;EACpB,CAAC;;EAED;EACA,MAAMC,aAA4B,GAAG,CACnC,CACE;IAAEC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,IAAI;IAAExB,UAAU,EAAE;EAAK,CAAC,EAChD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,IAAI;IAAExB,UAAU,EAAE;EAAK,CAAC,EAChD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,IAAI;IAAExB,UAAU,EAAE;EAAK,CAAC,EAChD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,IAAI;IAAExB,UAAU,EAAE;EAAK,CAAC,CACjD,EACD,CACE;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,MAAM;IAAExB,UAAU,EAAE;EAAK,CAAC,EAClD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,IAAI;IAAExB,UAAU,EAAE;EAAK,CAAC,EAChD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,OAAO;IAAExB,UAAU,EAAE;EAAK,CAAC,EACnD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,QAAQ;IAAExB,UAAU,EAAE;EAAK,CAAC,CACrD,EACD,CACE;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,MAAM;IAAExB,UAAU,EAAE;EAAK,CAAC,EAClD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,GAAG;IAAExB,UAAU,EAAE;EAAK,CAAC,EAC/C;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,SAAS;IAAExB,UAAU,EAAE;EAAK,CAAC,EACrD;IAAEyC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAElB,IAAI,EAAE,SAAS;IAAExB,UAAU,EAAE;EAAK,CAAC,CACtD,CACF;EAED,MAAM2C,YAA0C,GAAG,MAAOC,OAAO,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAGH,OAAO;IAE5C,IAAI;MACFnD,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM,IAAIuD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAY,CAAC;MAClD,MAAMS,WAA6B,GAAG;QACpC/B,EAAE,EAAEgC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,QAAQ,EAAGb,IAAI,CAAUb,IAAI;QAC7B2B,GAAG,EAAER,QAAQ;QACbS,IAAI,EAAGf,IAAI,CAAUe,IAAI;QACzB9B,IAAI,EAAE,SAAS;QAAE;QACjB+B,UAAU,EAAEvC,cAAc;QAC1BwC,SAAS,EAAE1D,WAAW,GAAGoC,aAAa,GAAGuB,SAAS;QAClDC,eAAe,EAAEjC,mBAAmB;QACpCkC,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACW,cAAc,CAAC,CAAC;QACtCC,cAAc,EAAE,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QACvCrE,UAAU,EAAE,IAAI,GAAGoE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MACrC,CAAC;MAEDhF,WAAW,CAACiF,IAAI,IAAI,CAAChB,WAAW,EAAE,GAAGgB,IAAI,CAAC,CAAC;MAC3C/E,mBAAmB,CAAC+D,WAAW,CAAC;MAEhCR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,CAAC;MACxBzG,OAAO,CAAC0H,OAAO,CAAC,GAAI1B,IAAI,CAAUb,IAAI,OAAO,CAAC;IAChD,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdzB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGyB,KAAc,CAAC;MACzB3H,OAAO,CAAC2H,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/E,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMgF,aAAa,GAAG,MAAO5B,IAAgB,IAAK;IAChD,IAAI,CAACA,IAAI,CAACc,GAAG,IAAI,CAACd,IAAI,CAAC6B,OAAO,EAAE;MAC9B7B,IAAI,CAAC6B,OAAO,GAAG,MAAMC,SAAS,CAAC9B,IAAI,CAAC+B,aAAqB,CAAC;IAC5D;IACA/E,eAAe,CAACgD,IAAI,CAACc,GAAG,IAAId,IAAI,CAAC6B,OAAO,IAAI,EAAE,CAAC;IAC/C/E,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMgF,SAAS,GAAI9B,IAAU,IAC3B,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAE4B,MAAM,KAAK;IAC/B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,aAAa,CAACnC,IAAI,CAAC;IAC1BiC,MAAM,CAACG,MAAM,GAAG,MAAMhC,OAAO,CAAC6B,MAAM,CAACI,MAAgB,CAAC;IACtDJ,MAAM,CAACK,OAAO,GAAGX,KAAK,IAAIK,MAAM,CAACL,KAAK,CAAC;EACzC,CAAC,CAAC;EAEJ,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UAAE7D,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI;MACnC,CAAC,CAAC;MACF,IAAIrB,SAAS,CAACkF,OAAO,EAAE;QACrBlF,SAAS,CAACkF,OAAO,CAACC,SAAS,GAAGN,MAAM;QACpC1E,iBAAiB,CAAC,IAAI,CAAC;QACvB9D,OAAO,CAAC0H,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3H,OAAO,CAAC2H,KAAK,CAAC,iBAAiB,CAAC;IAClC;EACF,CAAC;EAED,MAAMoB,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA;IACvB,KAAAA,kBAAA,GAAIrF,SAAS,CAACkF,OAAO,cAAAG,kBAAA,eAAjBA,kBAAA,CAAmBF,SAAS,EAAE;MAChC,MAAMN,MAAM,GAAG7E,SAAS,CAACkF,OAAO,CAACC,SAAwB;MACzDN,MAAM,CAACS,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACjDzF,SAAS,CAACkF,OAAO,CAACC,SAAS,GAAG,IAAI;MAClChF,iBAAiB,CAAC,KAAK,CAAC;MACxB9D,OAAO,CAACqJ,IAAI,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI3F,SAAS,CAACkF,OAAO,IAAIjF,SAAS,CAACiF,OAAO,EAAE;MAC1C,MAAMU,MAAM,GAAG3F,SAAS,CAACiF,OAAO;MAChC,MAAMD,KAAK,GAAGjF,SAAS,CAACkF,OAAO;MAC/B,MAAMW,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MAEvCF,MAAM,CAACxE,KAAK,GAAG6D,KAAK,CAACc,UAAU;MAC/BH,MAAM,CAACvE,MAAM,GAAG4D,KAAK,CAACe,WAAW;MACjCH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,SAAS,CAAChB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAE/BW,MAAM,CAACM,MAAM,CAACC,IAAI,IAAI;QACpB,IAAIA,IAAI,EAAE;UACR,MAAM9D,IAAI,GAAG,IAAI+D,IAAI,CAAC,CAACD,IAAI,CAAC,EAAE,YAAYpD,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM,EAAE;YAAE1B,IAAI,EAAE;UAAa,CAAC,CAAC;UACnFa,YAAY,CAAC;YACXE,IAAI;YACJC,SAAS,EAAEA,CAAA,KAAM,CAAC,CAAC;YACnBC,OAAO,EAAEA,CAAA,KAAM,CAAC;UAClB,CAAQ,CAAC;QACX;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM8D,QAAQ,GAAGA,CAAA,KAAM;IACrBxH,WAAW,CAAC,EAAE,CAAC;IACfE,mBAAmB,CAAC,IAAI,CAAC;IACzBJ,WAAW,CAAC,EAAE,CAAC;IACftC,OAAO,CAAC0H,OAAO,CAAC,WAAW,CAAC;EAC9B,CAAC;EAED,MAAMuC,WAAW,GAAIhF,IAAY,IAAK;IACpC,MAAMiF,UAAU,GAAGnG,aAAa,CAACoG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAKiB,IAAI,CAAC;IAC5D,OAAO,CAAAiF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhG,IAAI,kBAAI1C,OAAA,CAAChB,gBAAgB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjD,CAAC;EAED,MAAM+F,YAAY,GAAIpF,IAAY,IAAK;IACrC,MAAMiF,UAAU,GAAGnG,aAAa,CAACoG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAKiB,IAAI,CAAC;IAC5D,OAAO,CAAAiF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE3F,KAAK,KAAI,SAAS;EACvC,CAAC;EAED,MAAM+F,YAAY,GAAIrF,IAAY,IAAK;IACrC,MAAMiF,UAAU,GAAGnG,aAAa,CAACoG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAKiB,IAAI,CAAC;IAC5D,OAAO,CAAAiF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjG,KAAK,KAAI,MAAM;EACpC,CAAC;;EAED;EACA,MAAMsG,YAA8B,GAAG,EAAApI,eAAA,GAAAwD,aAAa,CAAC,CAAC,CAAC,cAAAxD,eAAA,uBAAhBA,eAAA,CAAkBqI,GAAG,CAAC,CAACC,CAAC,EAAEC,QAAQ,MAAM;IAC7EC,KAAK,EAAE,KAAKD,QAAQ,GAAG,CAAC,EAAE;IAC1BE,SAAS,EAAEF,QAAQ;IACnBG,GAAG,EAAEH,QAAQ;IACbI,MAAM,EAAEA,CAACnG,IAAY,EAAEoG,MAAW,EAAEC,QAAgB,KAAK;MAAA,IAAAC,qBAAA;MACvD,MAAMC,IAAI,IAAAD,qBAAA,GAAGtF,aAAa,CAACqF,QAAQ,CAAC,cAAAC,qBAAA,uBAAvBA,qBAAA,CAA0BP,QAAQ,CAAC;MAChD,oBACElJ,OAAA;QAAA2J,QAAA,gBACE3J,OAAA;UAAA2J,QAAA,EAAMD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEvG;QAAI;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvB9C,OAAA;UAAK4J,KAAK,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAE9G,KAAK,EAAE;UAAO,CAAE;UAAA4G,QAAA,GAAC,sBACtC,EAAC,CAAC,CAAC,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/H,UAAU,KAAI,CAAC,IAAI,GAAG,EAAEmI,OAAO,CAAC,CAAC,CAAC,EAAC,GACnD;QAAA;UAAAnH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;EACF,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMiH,eAAe,GAAG5F,aAAa,CAAC6E,GAAG,CAAC,CAAC5E,GAAG,EAAE4F,KAAK,MAAM;IACzDX,GAAG,EAAEW,KAAK;IACV,GAAG5F,GAAG,CAAC6F,MAAM,CAAC,CAACC,GAAG,EAAER,IAAI,EAAER,QAAQ,KAAK;MACrCgB,GAAG,CAAChB,QAAQ,CAAC,GAAGQ,IAAI,CAACvG,IAAI;MACzB,OAAO+G,GAAG;IACZ,CAAC,EAAE,CAAC,CAA2B;EACjC,CAAC,CAAC,CAAC;EAEH,oBACElK,OAAA;IAAA2J,QAAA,gBACE3J,OAAA;MAAK4J,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAG,CAAE;MAAAR,QAAA,eAC/B3J,OAAA,CAACpC,KAAK;QAAA+L,QAAA,eACJ3J,OAAA,CAACrC,MAAM;UACL+E,IAAI,eAAE1C,OAAA,CAACjB,iBAAiB;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BsH,OAAO,EAAEA,CAAA,KAAMxJ,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAA+I,QAAA,EAC7B;QAED;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN9C,OAAA,CAACnC,GAAG;MAACwM,MAAM,EAAE,EAAG;MAAAV,QAAA,gBACd3J,OAAA,CAAClC,GAAG;QAACwM,IAAI,EAAE,EAAG;QAAAX,QAAA,eACZ3J,OAAA,CAACtC,IAAI;UAACyL,KAAK,eACTnJ,OAAA,CAACpC,KAAK;YAAA+L,QAAA,gBACJ3J,OAAA,CAAChB,gBAAgB;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpB9C,OAAA;cAAA2J,QAAA,EAAM;YAAI;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UAAA6G,QAAA,eACC3J,OAAA,CAAC5B,IAAI;YAACmM,gBAAgB,EAAC,QAAQ;YAAAZ,QAAA,gBAC7B3J,OAAA,CAAC5B,IAAI,CAACoM,OAAO;cAACC,GAAG,EAAC,0BAAM;cAAAd,QAAA,gBACtB3J,OAAA,CAACjC,MAAM,CAAC2M,OAAO;gBACb/G,IAAI,EAAC,MAAM;gBACXgH,QAAQ;gBACRC,MAAM,EAAC,yBAAyB;gBAChCC,aAAa,EAAEvG,YAAa;gBAC5BwG,SAAS,EAAE1E,aAAc;gBACzBvF,QAAQ,EAAEA,QAAS;gBACnBkK,QAAQ,EAAEA,CAAC;kBAAElK;gBAAS,CAAC,KAAKC,WAAW,CAACD,QAAQ,CAAE;gBAClDmK,QAAQ,EAAE7J,WAAY;gBAAAwI,QAAA,gBAEtB3J,OAAA;kBAAGiL,SAAS,EAAC,sBAAsB;kBAAAtB,QAAA,eACjC3J,OAAA,CAACf,cAAc;oBAAC2K,KAAK,EAAE;sBAAEC,QAAQ,EAAE,EAAE;sBAAE9G,KAAK,EAAE;oBAAU;kBAAE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACJ9C,OAAA;kBAAGiL,SAAS,EAAC,iBAAiB;kBAAAtB,QAAA,EAAC;gBAAa;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChD9C,OAAA;kBAAGiL,SAAS,EAAC,iBAAiB;kBAAAtB,QAAA,EAAC;gBAE/B;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,EAEhB3B,WAAW,iBACVnB,OAAA;gBAAK4J,KAAK,EAAE;kBAAEsB,SAAS,EAAE,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,gBACjD3J,OAAA,CAAC7B,QAAQ;kBAACsF,IAAI,EAAC,QAAQ;kBAAC2H,OAAO,EAAE;gBAAG;kBAAAzI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvC9C,OAAA;kBAAK4J,KAAK,EAAE;oBAAEsB,SAAS,EAAE;kBAAE,CAAE;kBAAAvB,QAAA,eAC3B3J,OAAA,CAACK,IAAI;oBAAAsJ,QAAA,EAAC;kBAAa;oBAAAhH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GA3B0B,QAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BvB,CAAC,eAEf9C,OAAA,CAAC5B,IAAI,CAACoM,OAAO;cAACC,GAAG,EAAC,gCAAO;cAAAd,QAAA,eACvB3J,OAAA;gBAAK4J,KAAK,EAAE;kBAAEuB,SAAS,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,gBAClC3J,OAAA;kBAAK4J,KAAK,EAAE;oBAAEO,YAAY,EAAE;kBAAG,CAAE;kBAAAR,QAAA,gBAC/B3J,OAAA;oBACEqL,GAAG,EAAElJ,SAAU;oBACfmJ,QAAQ;oBACRC,WAAW;oBACX3B,KAAK,EAAE;sBACLrG,KAAK,EAAE,MAAM;sBACbiI,QAAQ,EAAE,GAAG;sBACbhI,MAAM,EAAE,MAAM;sBACdiI,MAAM,EAAE,mBAAmB;sBAC3BC,YAAY,EAAE,CAAC;sBACfC,OAAO,EAAEtJ,cAAc,GAAG,OAAO,GAAG;oBACtC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF9C,OAAA;oBAAQqL,GAAG,EAAEjJ,SAAU;oBAACwH,KAAK,EAAE;sBAAE+B,OAAO,EAAE;oBAAO;kBAAE;oBAAAhJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAErD,CAACT,cAAc,iBACdrC,OAAA;oBAAK4J,KAAK,EAAE;sBACVrG,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,GAAG;sBACXiI,MAAM,EAAE,oBAAoB;sBAC5BC,YAAY,EAAE,CAAC;sBACfC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBC,aAAa,EAAE,QAAQ;sBACvB/I,KAAK,EAAE;oBACT,CAAE;oBAAA4G,QAAA,gBACA3J,OAAA,CAACd,cAAc;sBAAC0K,KAAK,EAAE;wBAAEC,QAAQ,EAAE,EAAE;wBAAEM,YAAY,EAAE;sBAAG;oBAAE;sBAAAxH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7D9C,OAAA,CAACK,IAAI;sBAACoD,IAAI,EAAC,WAAW;sBAAAkG,QAAA,EAAC;oBAAW;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN9C,OAAA,CAACpC,KAAK;kBAAA+L,QAAA,EACH,CAACtH,cAAc,gBACdrC,OAAA,CAACrC,MAAM;oBACL8F,IAAI,EAAC,SAAS;oBACdf,IAAI,eAAE1C,OAAA,CAACd,cAAc;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBsH,OAAO,EAAErD,WAAY;oBAAA4C,QAAA,EACtB;kBAED;oBAAAhH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAET9C,OAAA,CAAAE,SAAA;oBAAAyJ,QAAA,gBACE3J,OAAA,CAACrC,MAAM;sBACL8F,IAAI,EAAC,SAAS;sBACdf,IAAI,eAAE1C,OAAA,CAACd,cAAc;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzBsH,OAAO,EAAEtC,YAAa;sBACtBiE,OAAO,EAAE5K,WAAY;sBAAAwI,QAAA,EACtB;oBAED;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT9C,OAAA,CAACrC,MAAM;sBACL+E,IAAI,eAAE1C,OAAA,CAACb,cAAc;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzBsH,OAAO,EAAE7C,UAAW;sBAAAoC,QAAA,EACrB;oBAED;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GAhEsB,QAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiExB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9C,OAAA,CAAClC,GAAG;QAACwM,IAAI,EAAE,CAAE;QAAAX,QAAA,gBACX3J,OAAA,CAACtC,IAAI;UAACyL,KAAK,EAAC,0BAAM;UAAC5D,IAAI,EAAC,OAAO;UAAAoE,QAAA,eAC7B3J,OAAA,CAACpC,KAAK;YAACoO,SAAS,EAAC,UAAU;YAACpC,KAAK,EAAE;cAAErG,KAAK,EAAE;YAAO,CAAE;YAAAoG,QAAA,gBACnD3J,OAAA;cAAA2J,QAAA,gBACE3J,OAAA,CAACK,IAAI;gBAAC4L,MAAM;gBAAAtC,QAAA,EAAC;cAAI;gBAAAhH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxB9C,OAAA,CAAC3B,MAAM;gBACLmE,KAAK,EAAEP,QAAS;gBAChB8I,QAAQ,EAAE7I,WAAY;gBACtB0H,KAAK,EAAE;kBAAErG,KAAK,EAAE,MAAM;kBAAE2H,SAAS,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EAEtC3G,eAAe,CAACgG,GAAG,CAACkD,IAAI,iBACvBlM,OAAA,CAACM,MAAM;kBAAkBkC,KAAK,EAAE0J,IAAI,CAAC1J,KAAM;kBAAAmH,QAAA,EACxCuC,IAAI,CAACzJ;gBAAK,GADAyJ,IAAI,CAAC1J,KAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9C,OAAA;cAAA2J,QAAA,gBACE3J,OAAA,CAACK,IAAI;gBAAC4L,MAAM;gBAAAtC,QAAA,EAAC;cAAK;gBAAAhH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzB9C,OAAA,CAACzB,MAAM;gBACLiE,KAAK,EAAEb,UAAW;gBAClBoJ,QAAQ,EAAEnJ,aAAc;gBACxBgI,KAAK,EAAE;kBAAEsB,SAAS,EAAE;gBAAE,CAAE;gBACxBiB,OAAO,EAAE;kBAAEC,SAAS,EAAG5J,KAAK,IAAK,GAAGA,KAAK;gBAAI;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF9C,OAAA,CAACK,IAAI;gBAACoD,IAAI,EAAC,WAAW;gBAACmG,KAAK,EAAE;kBAAEC,QAAQ,EAAE;gBAAG,CAAE;gBAAAF,QAAA,GAAC,gBAC1C,EAAChI,UAAU,EAAC,GAClB;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN9C,OAAA;cAAA2J,QAAA,eACE3J,OAAA,CAACpC,KAAK;gBAACgM,KAAK,EAAE;kBAAErG,KAAK,EAAE,MAAM;kBAAEsI,cAAc,EAAE;gBAAgB,CAAE;gBAAAlC,QAAA,gBAC/D3J,OAAA,CAACK,IAAI;kBAAC4L,MAAM;kBAAAtC,QAAA,EAAC;gBAAK;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzB9C,OAAA,CAAC1B,MAAM;kBACL+N,OAAO,EAAExK,eAAgB;kBACzBkJ,QAAQ,EAAEjJ;gBAAmB;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9C,OAAA;cAAA2J,QAAA,eACE3J,OAAA,CAACpC,KAAK;gBAACgM,KAAK,EAAE;kBAAErG,KAAK,EAAE,MAAM;kBAAEsI,cAAc,EAAE;gBAAgB,CAAE;gBAAAlC,QAAA,gBAC/D3J,OAAA,CAACK,IAAI;kBAAC4L,MAAM;kBAAAtC,QAAA,EAAC;gBAAI;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxB9C,OAAA,CAAC1B,MAAM;kBACL+N,OAAO,EAAEtK,WAAY;kBACrBgJ,QAAQ,EAAE/I;gBAAe;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP9C,OAAA,CAACtC,IAAI;UAACyL,KAAK,EAAC,0BAAM;UAAC5D,IAAI,EAAC,OAAO;UAACqE,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAG,CAAE;UAAAvB,QAAA,eACvD3J,OAAA,CAACnC,GAAG;YAACwM,MAAM,EAAE,EAAG;YAAAV,QAAA,gBACd3J,OAAA,CAAClC,GAAG;cAACwM,IAAI,EAAE,EAAG;cAAAX,QAAA,eACZ3J,OAAA,CAACtB,SAAS;gBACRyK,KAAK,EAAC,oBAAK;gBACX3G,KAAK,EAAEzB,QAAQ,CAACuL,MAAO;gBACvBC,MAAM,eAAEvM,OAAA,CAAChB,gBAAgB;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7B0J,MAAM,EAAC;cAAG;gBAAA7J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9C,OAAA,CAAClC,GAAG;cAACwM,IAAI,EAAE,EAAG;cAAAX,QAAA,eACZ3J,OAAA,CAACtB,SAAS;gBACRyK,KAAK,EAAC,gCAAO;gBACb3G,KAAK,EAAEzB,QAAQ,CAACuL,MAAM,GAAG,CAAC,GACxB,CAACvL,QAAQ,CAACkJ,MAAM,CAAC,CAACwC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC/K,UAAU,EAAE,CAAC,CAAC,GAAGZ,QAAQ,CAACuL,MAAM,GAAG,GAAG,EAAExC,OAAO,CAAC,CAAC,CAAC,GACvF,CACD;gBACD0C,MAAM,EAAC,GAAG;gBACVD,MAAM,eAAEvM,OAAA,CAACT,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAACtC,IAAI;MACHyL,KAAK,eACHnJ,OAAA,CAACpC,KAAK;QAAA+L,QAAA,gBACJ3J,OAAA,CAAChB,gBAAgB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpB9C,OAAA;UAAA2J,QAAA,EAAM;QAAI;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjB9C,OAAA,CAACrB,KAAK;UAACgO,KAAK,EAAE5L,QAAQ,CAACuL;QAAO;UAAA3J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACR;MACD8J,KAAK,eACH5M,OAAA,CAACpC,KAAK;QAAA+L,QAAA,gBACJ3J,OAAA,CAACrC,MAAM;UACL+E,IAAI,eAAE1C,OAAA,CAACZ,gBAAgB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BsH,OAAO,EAAEA,CAAA,KAAM5L,OAAO,CAACqJ,IAAI,CAAC,SAAS,CAAE;UAAA8B,QAAA,EACxC;QAED;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAACrC,MAAM;UACL+E,IAAI,eAAE1C,OAAA,CAACV,aAAa;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBsH,OAAO,EAAE5B,QAAS;UAClBwC,QAAQ,EAAEjK,QAAQ,CAACuL,MAAM,KAAK,CAAE;UAAA3C,QAAA,EACjC;QAED;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MACD8G,KAAK,EAAE;QAAEsB,SAAS,EAAE;MAAG,CAAE;MAAAvB,QAAA,EAExB5I,QAAQ,CAACuL,MAAM,KAAK,CAAC,gBACpBtM,OAAA;QAAK4J,KAAK,EAAE;UAAEuB,SAAS,EAAE,QAAQ;UAAE0B,OAAO,EAAE,QAAQ;UAAE9J,KAAK,EAAE;QAAO,CAAE;QAAA4G,QAAA,gBACpE3J,OAAA,CAAChB,gBAAgB;UAAC4K,KAAK,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAEM,YAAY,EAAE;UAAG;QAAE;UAAAxH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/D9C,OAAA;UAAA2J,QAAA,EAAK;QAAM;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjB9C,OAAA;UAAK4J,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAF,QAAA,EAAC;QAAiB;UAAAhH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,gBAEN9C,OAAA,CAACnC,GAAG;QAACwM,MAAM,EAAE,EAAG;QAAAV,QAAA,gBACd3J,OAAA,CAAClC,GAAG;UAACwM,IAAI,EAAE,CAAE;UAAAX,QAAA,eACX3J,OAAA,CAAC/B,IAAI;YACHsH,IAAI,EAAC,OAAO;YACZuH,UAAU,EAAE/L,QAAS;YACrBgM,UAAU,EAAGC,IAAI,iBACfhN,OAAA,CAAC/B,IAAI,CAACgP,IAAI;cACRrD,KAAK,EAAE;gBACLsD,MAAM,EAAE,SAAS;gBACjBC,eAAe,EAAE,CAAAlM,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiC,EAAE,MAAK8J,IAAI,CAAC9J,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC7E2J,OAAO,EAAE,UAAU;gBACnBnB,YAAY,EAAE;cAChB,CAAE;cACFtB,OAAO,EAAEA,CAAA,KAAMlJ,mBAAmB,CAAC8L,IAAI,CAAE;cAAArD,QAAA,eAEzC3J,OAAA,CAAC/B,IAAI,CAACgP,IAAI,CAACG,IAAI;gBACbC,MAAM,eACJrN,OAAA;kBAAK4J,KAAK,EAAE;oBACVrG,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVkI,YAAY,EAAE,KAAK;oBACnByB,eAAe,EAAEtE,YAAY,CAACmE,IAAI,CAACvJ,IAAI,CAAC;oBACxCkI,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxB9I,KAAK,EAAE;kBACT,CAAE;kBAAA4G,QAAA,EACClB,WAAW,CAACuE,IAAI,CAACvJ,IAAI;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CACN;gBACDqG,KAAK,eACHnJ,OAAA,CAACpC,KAAK;kBAAA+L,QAAA,gBACJ3J,OAAA,CAACK,IAAI;oBAAC4L,MAAM;oBAACrC,KAAK,EAAE;sBAAEC,QAAQ,EAAE;oBAAG,CAAE;oBAAAF,QAAA,EAClCqD,IAAI,CAAC3H;kBAAQ;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACP9C,OAAA,CAAC9B,GAAG;oBAAC6E,KAAK,EAAE8F,YAAY,CAACmE,IAAI,CAACvJ,IAAI,CAAE;oBAAAkG,QAAA,EACjCb,YAAY,CAACkE,IAAI,CAACvJ,IAAI;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR;gBACDwK,WAAW,eACTtN,OAAA;kBAAA2J,QAAA,gBACE3J,OAAA,CAACK,IAAI;oBAACoD,IAAI,EAAC,WAAW;oBAACmG,KAAK,EAAE;sBAAEC,QAAQ,EAAE;oBAAG,CAAE;oBAAAF,QAAA,EAC5CqD,IAAI,CAACpH;kBAAS;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACP9C,OAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9C,OAAA,CAACK,IAAI;oBAACoD,IAAI,EAAC,WAAW;oBAACmG,KAAK,EAAE;sBAAEC,QAAQ,EAAE;oBAAG,CAAE;oBAAAF,QAAA,GAAC,sBACzC,EAAC,CAACqD,IAAI,CAACrL,UAAU,GAAG,GAAG,EAAEmI,OAAO,CAAC,CAAC,CAAC,EAAC,GAC3C;kBAAA;oBAAAnH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9C,OAAA,CAAClC,GAAG;UAACwM,IAAI,EAAE,EAAG;UAAAX,QAAA,EACX1I,gBAAgB,iBACfjB,OAAA;YAAA2J,QAAA,gBACE3J,OAAA;cAAK4J,KAAK,EAAE;gBAAEO,YAAY,EAAE,EAAE;gBAAEgB,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,eACpD3J,OAAA,CAAChC,KAAK;gBACJuP,GAAG,EAAEtM,gBAAgB,CAACqE,GAAI;gBAC1BkI,GAAG,EAAEvM,gBAAgB,CAACoE,QAAS;gBAC/BuE,KAAK,EAAE;kBAAE4B,QAAQ,EAAE,MAAM;kBAAEiC,SAAS,EAAE;gBAAI,CAAE;gBAC5CpH,OAAO,EAAE;kBACPqH,IAAI,eACF1N,OAAA,CAACpC,KAAK;oBAAA+L,QAAA,gBACJ3J,OAAA,CAACX,cAAc;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEpB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAEX;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9C,OAAA,CAAC5B,IAAI;cAACmM,gBAAgB,EAAC,KAAK;cAAAZ,QAAA,gBAC1B3J,OAAA,CAAC5B,IAAI,CAACoM,OAAO;gBAACC,GAAG,EAAC,iBAAO;gBAAAd,QAAA,eACvB3J,OAAA,CAAC/B,IAAI;kBACHsH,IAAI,EAAC,OAAO;kBACZuH,UAAU,EAAE7L,gBAAgB,CAACuE,UAAW;kBACxCuH,UAAU,EAAGlG,MAAM,iBACjB7G,OAAA,CAAC/B,IAAI,CAACgP,IAAI;oBACRU,OAAO,EAAE,cACP3N,OAAA,CAACrC,MAAM;sBAEL8F,IAAI,EAAC,MAAM;sBACX8B,IAAI,EAAC,OAAO;sBACZ7C,IAAI,eAAE1C,OAAA,CAACH,YAAY;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACvBsH,OAAO,EAAEA,CAAA,KAAM;wBACbnD,SAAS,CAAC2G,SAAS,CAACC,SAAS,CAAChH,MAAM,CAAC1D,IAAI,CAAC;wBAC1C3E,OAAO,CAAC0H,OAAO,CAAC,SAAS,CAAC;sBAC5B;oBAAE,GAPE,MAAM;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQX,CAAC,CACF;oBAAA6G,QAAA,eAEF3J,OAAA,CAAC/B,IAAI,CAACgP,IAAI,CAACG,IAAI;sBACbjE,KAAK,eACHnJ,OAAA,CAACpC,KAAK;wBAAA+L,QAAA,gBACJ3J,OAAA,CAACK,IAAI;0BAAAsJ,QAAA,EAAE9C,MAAM,CAAC1D;wBAAI;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC1B9C,OAAA,CAAC9B,GAAG;0BAAC6E,KAAK,EAAE8D,MAAM,CAAClF,UAAU,IAAI,GAAG,GAAG,OAAO,GAAGkF,MAAM,CAAClF,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAM;0BAAAgI,QAAA,GAC1F,CAAC9C,MAAM,CAAClF,UAAU,GAAG,GAAG,EAAEmI,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;wBAAA;0BAAAnH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACN9C,OAAA,CAAC9B,GAAG;0BAAC6E,KAAK,EAAC,MAAM;0BAAA4G,QAAA,EAAE9C,MAAM,CAACpD;wBAAI;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CACR;sBACDwK,WAAW,eACTtN,OAAA,CAACK,IAAI;wBAACoD,IAAI,EAAC,WAAW;wBAACmG,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAG,CAAE;wBAAAF,QAAA,GAAC,iBACzC,EAAC9C,MAAM,CAACzD,IAAI,CAACC,CAAC,EAAC,IAAE,EAACwD,MAAM,CAACzD,IAAI,CAACE,CAAC,EAAC,kBACjC,EAACuD,MAAM,CAACzD,IAAI,CAACG,KAAK,EAAC,MAAC,EAACsD,MAAM,CAACzD,IAAI,CAACI,MAAM;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBACP;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBACX;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAtC0B,KAAK;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCrB,CAAC,EAEd7B,gBAAgB,CAAC0E,eAAe,iBAC/B3F,OAAA,CAAC5B,IAAI,CAACoM,OAAO;gBAACC,GAAG,EAAC,gCAAO;gBAAAd,QAAA,eACvB3J,OAAA,CAAClB,QAAQ;kBAACyL,gBAAgB,EAAE,CAAC,GAAG,CAAE;kBAAAZ,QAAA,eAChC3J,OAAA,CAACQ,KAAK;oBAACsN,MAAM,EAAC,0BAAM;oBAAAnE,QAAA,eAClB3J,OAAA,CAACnC,GAAG;sBAACwM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;sBAAAV,QAAA,EACnBoE,MAAM,CAACC,OAAO,CAAC/M,gBAAgB,CAAC0E,eAAe,CAAC,CAACqD,GAAG,CAAC,CAAC,CAACK,GAAG,EAAE7G,KAAK,CAAC,kBACjExC,OAAA,CAAClC,GAAG;wBAACwM,IAAI,EAAE,EAAG;wBAAAX,QAAA,eACZ3J,OAAA;0BAAK4J,KAAK,EAAE;4BACViD,OAAO,EAAE,EAAE;4BACXpB,MAAM,EAAE,mBAAmB;4BAC3BC,YAAY,EAAE,CAAC;4BACfyB,eAAe,EAAE;0BACnB,CAAE;0BAAAxD,QAAA,gBACA3J,OAAA;4BAAK4J,KAAK,EAAE;8BAAEC,QAAQ,EAAE,EAAE;8BAAE9G,KAAK,EAAE,MAAM;8BAAEoH,YAAY,EAAE;4BAAE,CAAE;4BAAAR,QAAA,EAC1DN;0BAAG;4BAAA1G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC,eACN9C,OAAA;4BAAK4J,KAAK,EAAE;8BAAEqE,UAAU,EAAE;4BAAI,CAAE;4BAAAtE,QAAA,EAC7BnH;0BAAK;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC,GAbYuG,GAAG;wBAAA1G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAclB,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC,GAnBiB,GAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoBrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC,GAvBiB,QAAQ;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBxB,CACf,EAEA7B,gBAAgB,CAACwE,SAAS,iBACzBzF,OAAA,CAAC5B,IAAI,CAACoM,OAAO;gBAACC,GAAG,EAAC,0BAAM;gBAAAd,QAAA,eACtB3J,OAAA,CAACpB,KAAK;kBACJsP,OAAO,EAAEnF,YAAa;kBACtB+D,UAAU,EAAE/C,eAAgB;kBAC5BxE,IAAI,EAAC,OAAO;kBACZ4I,UAAU,EAAE,KAAM;kBAClBC,QAAQ;gBAAA;kBAAAzL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC,GAPyB,OAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQtB,CACf;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGP9C,OAAA,CAACvB,KAAK;MACJ4P,IAAI,EAAEhN,cAAe;MACrB8H,KAAK,EAAC,0BAAM;MACZmF,MAAM,EAAE,IAAK;MACbC,QAAQ,EAAEA,CAAA,KAAMjN,iBAAiB,CAAC,KAAK,CAAE;MACzCiC,KAAK,EAAE,GAAI;MAAAoG,QAAA,eAEX3J,OAAA;QAAKwN,GAAG,EAAC,SAAS;QAAC5D,KAAK,EAAE;UAAErG,KAAK,EAAE;QAAO,CAAE;QAACgK,GAAG,EAAEhM;MAAa;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpC,EAAA,CAnqBID,mBAA6B;EAAA,QAChBX,WAAW;AAAA;AAAA0O,EAAA,GADxB/N,mBAA6B;AAqqBnC,eAAeA,mBAAmB;AAAC,IAAA+N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}