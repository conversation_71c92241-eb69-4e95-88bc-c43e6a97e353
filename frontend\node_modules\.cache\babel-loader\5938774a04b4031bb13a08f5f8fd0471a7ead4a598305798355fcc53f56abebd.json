{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Agent\\\\AgentList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Typography, Card, Button, Space, Input, Select } from 'antd';\nimport { PlusOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAppDispatch } from '../../store';\nimport { ROUTES } from '../../utils/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\n\n// 临时类型定义，直到我们修复slice\n\nconst AgentList = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n\n  // 使用本地状态暂时替代Redux状态\n  const [loading, setLoading] = useState(false);\n  const [agents, setAgents] = useState([]);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [filters, setLocalFilters] = useState({\n    search: '',\n    status: '',\n    type: ''\n  });\n  const [pagination, setLocalPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n          style: {\n            marginRight: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), \"\\u667A\\u80FD\\u4F53\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          margin: '8px 0 0 0'\n        },\n        children: \"\\u7BA1\\u7406\\u548C\\u76D1\\u63A7\\u60A8\\u7684\\u667A\\u80FD\\u4F53\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n          style: {\n            fontSize: 64,\n            color: '#1890ff',\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          children: \"\\u667A\\u80FD\\u4F53\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: 24\n          },\n          children: \"\\u8FD9\\u91CC\\u5C06\\u663E\\u793A\\u60A8\\u7684\\u667A\\u80FD\\u4F53\\u5217\\u8868\\uFF0C\\u5305\\u62EC\\u521B\\u5EFA\\u3001\\u7F16\\u8F91\\u3001\\u5220\\u9664\\u548C\\u76D1\\u63A7\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate(ROUTES.AGENT_CREATE),\n            children: \"\\u521B\\u5EFA\\u667A\\u80FD\\u4F53\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentList, \"F4EcGIip/vQMfXEtjCkX5K+Q9io=\", false, function () {\n  return [useNavigate, useAppDispatch];\n});\n_c = AgentList;\nexport default AgentList;\nvar _c;\n$RefreshReg$(_c, \"AgentList\");", "map": {"version": 3, "names": ["React", "useState", "Typography", "Card", "<PERSON><PERSON>", "Space", "Input", "Select", "PlusOutlined", "RobotOutlined", "useNavigate", "useAppDispatch", "ROUTES", "jsxDEV", "_jsxDEV", "Title", "Search", "Option", "AgentList", "_s", "navigate", "dispatch", "loading", "setLoading", "agents", "setAgents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "filters", "setLocalFilters", "search", "status", "type", "pagination", "setLocalPagination", "current", "pageSize", "total", "children", "style", "marginBottom", "level", "margin", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "textAlign", "padding", "fontSize", "icon", "onClick", "AGENT_CREATE", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Agent/AgentList.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  Typo<PERSON>,\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Input,\n  Select,\n  Row,\n  Col,\n  Statistic,\n  Avatar,\n  Tooltip,\n  Popconfirm,\n  message,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  EyeOutlined,\n  RobotOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAppDispatch, useAppSelector } from '../../store';\nimport { fetchAgents, deleteAgent, setFilters, setPagination } from '../../store/slices/agentSlice';\nimport { ROUTES } from '../../utils/constants';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\n// 临时类型定义，直到我们修复slice\ninterface Agent {\n  id: string;\n  name: string;\n  description: string;\n  type: 'chatbot' | 'workflow' | 'recognition' | 'analysis';\n  status: 'active' | 'inactive' | 'training' | 'error';\n  capabilities: string[];\n  model: string;\n  version: string;\n  createdAt: string;\n  updatedAt: string;\n  metrics: {\n    totalRequests: number;\n    successRate: number;\n    averageResponseTime: number;\n    lastActiveAt: string;\n  };\n}\n\nconst AgentList: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n\n  // 使用本地状态暂时替代Redux状态\n  const [loading, setLoading] = useState(false);\n  const [agents, setAgents] = useState<Agent[]>([]);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [filters, setLocalFilters] = useState({\n    search: '',\n    status: '',\n    type: '',\n  });\n  const [pagination, setLocalPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Title level={2} style={{ margin: 0 }}>\n          <RobotOutlined style={{ marginRight: 8 }} />\n          智能体管理\n        </Title>\n        <p style={{ color: '#666', margin: '8px 0 0 0' }}>管理和监控您的智能体</p>\n      </div>\n\n      <Card>\n        <div style={{ textAlign: 'center', padding: '40px 0' }}>\n          <RobotOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />\n          <Title level={3}>智能体管理</Title>\n          <p style={{ color: '#666', marginBottom: 24 }}>\n            这里将显示您的智能体列表，包括创建、编辑、删除和监控功能\n          </p>\n          <Space>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => navigate(ROUTES.AGENT_CREATE)}\n            >\n              创建智能体\n            </Button>\n          </Space>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AgentList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,QAAQ,QAAQ,OAAO;AAClD,SACEC,UAAU,EACVC,IAAI,EAEJC,MAAM,EACNC,KAAK,EAELC,KAAK,EACLC,MAAM,QAQD,MAAM;AACb,SACEC,YAAY,EAOZC,aAAa,QACR,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,QAAwB,aAAa;AAE5D,SAASC,MAAM,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG/C,MAAM;EAAEC;AAAM,CAAC,GAAGb,UAAU;AAC5B,MAAM;EAAEc;AAAO,CAAC,GAAGV,KAAK;AACxB,MAAM;EAAEW;AAAO,CAAC,GAAGV,MAAM;;AAEzB;;AAoBA,MAAMW,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,cAAc,CAAC,CAAC;;EAEjC;EACA,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC2B,OAAO,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC;IAC1C6B,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC;IAChDkC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,oBACEvB,OAAA;IAAAwB,QAAA,gBACExB,OAAA;MAAKyB,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAF,QAAA,gBAC/BxB,OAAA,CAACC,KAAK;QAAC0B,KAAK,EAAE,CAAE;QAACF,KAAK,EAAE;UAAEG,MAAM,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACpCxB,OAAA,CAACL,aAAa;UAAC8B,KAAK,EAAE;YAAEI,WAAW,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjC,OAAA;QAAGyB,KAAK,EAAE;UAAES,KAAK,EAAE,MAAM;UAAEN,MAAM,EAAE;QAAY,CAAE;QAAAJ,QAAA,EAAC;MAAU;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eAENjC,OAAA,CAACX,IAAI;MAAAmC,QAAA,eACHxB,OAAA;QAAKyB,KAAK,EAAE;UAAEU,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBACrDxB,OAAA,CAACL,aAAa;UAAC8B,KAAK,EAAE;YAAEY,QAAQ,EAAE,EAAE;YAAEH,KAAK,EAAE,SAAS;YAAER,YAAY,EAAE;UAAG;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EjC,OAAA,CAACC,KAAK;UAAC0B,KAAK,EAAE,CAAE;UAAAH,QAAA,EAAC;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9BjC,OAAA;UAAGyB,KAAK,EAAE;YAAES,KAAK,EAAE,MAAM;YAAER,YAAY,EAAE;UAAG,CAAE;UAAAF,QAAA,EAAC;QAE/C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjC,OAAA,CAACT,KAAK;UAAAiC,QAAA,eACJxB,OAAA,CAACV,MAAM;YACL4B,IAAI,EAAC,SAAS;YACdoB,IAAI,eAAEtC,OAAA,CAACN,YAAY;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBM,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAACR,MAAM,CAAC0C,YAAY,CAAE;YAAAhB,QAAA,EAC9C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAjDID,SAAmB;EAAA,QACNR,WAAW,EACXC,cAAc;AAAA;AAAA4C,EAAA,GAF3BrC,SAAmB;AAmDzB,eAAeA,SAAS;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}