{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Recognition\\\\VisionRecognition.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Typography, Card, Button, Space, Row, Col, Upload, Image, List, Tag, Progress, Tabs, Select, Switch, Slider, message, Modal, Divider, Statistic, Badge } from 'antd';\nimport { ArrowLeftOutlined, EyeOutlined, UploadOutlined, CameraOutlined, DeleteOutlined, DownloadOutlined, ZoomInOutlined, ClearOutlined, ScanOutlined, PictureOutlined, UserOutlined, CarOutlined, HomeOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst VisionRecognition = () => {\n  _s();\n  const navigate = useNavigate();\n  const [fileList, setFileList] = useState([]);\n  const [analyses, setAnalyses] = useState([]);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [selectedTypes, setSelectedTypes] = useState(['object', 'face', 'text', 'scene']);\n  const [confidence, setConfidence] = useState(70);\n  const [enableRealtime, setEnableRealtime] = useState(true);\n  const [maxResults, setMaxResults] = useState(10);\n  const cameraRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n\n  // 识别类型配置\n  const recognitionTypes = [{\n    value: 'object',\n    label: '物体识别',\n    icon: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 45\n    }, this),\n    color: '#1890ff'\n  }, {\n    value: 'face',\n    label: '人脸识别',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 43\n    }, this),\n    color: '#52c41a'\n  }, {\n    value: 'text',\n    label: '文字识别',\n    icon: /*#__PURE__*/_jsxDEV(PictureOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 43\n    }, this),\n    color: '#faad14'\n  }, {\n    value: 'scene',\n    label: '场景识别',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 44\n    }, this),\n    color: '#722ed1'\n  }, {\n    value: 'landmark',\n    label: '地标识别',\n    icon: /*#__PURE__*/_jsxDEV(CarOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 47\n    }, this),\n    color: '#eb2f96'\n  }];\n\n  // 模拟识别结果\n  const mockResults = [{\n    id: '1',\n    type: 'object',\n    label: '汽车',\n    confidence: 0.95,\n    bbox: {\n      x: 100,\n      y: 50,\n      width: 200,\n      height: 150\n    },\n    attributes: {\n      color: '红色',\n      brand: '丰田'\n    }\n  }, {\n    id: '2',\n    type: 'face',\n    label: '人脸',\n    confidence: 0.88,\n    bbox: {\n      x: 300,\n      y: 80,\n      width: 80,\n      height: 100\n    },\n    attributes: {\n      age: '25-35',\n      gender: '女性',\n      emotion: '微笑'\n    }\n  }, {\n    id: '3',\n    type: 'text',\n    label: '停车场',\n    confidence: 0.92,\n    bbox: {\n      x: 50,\n      y: 20,\n      width: 120,\n      height: 30\n    }\n  }, {\n    id: '4',\n    type: 'scene',\n    label: '城市街道',\n    confidence: 0.85\n  }];\n  const handleUpload = async options => {\n    const {\n      file,\n      onSuccess,\n      onError\n    } = options;\n    try {\n      setIsAnalyzing(true);\n\n      // 模拟文件上传和分析\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const imageUrl = URL.createObjectURL(file);\n      const newAnalysis = {\n        id: Date.now().toString(),\n        filename: file.name,\n        url: imageUrl,\n        size: file.size,\n        dimensions: {\n          width: 800,\n          height: 600\n        },\n        // 模拟尺寸\n        results: mockResults.filter(r => selectedTypes.includes(r.type)),\n        timestamp: new Date().toLocaleString(),\n        processingTime: 1.5 + Math.random() * 2\n      };\n      setAnalyses(prev => [newAnalysis, ...prev]);\n      setSelectedImage(newAnalysis);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(newAnalysis);\n      message.success(`${file.name} 分析完成`);\n    } catch (error) {\n      onError === null || onError === void 0 ? void 0 : onError(error);\n      message.error('分析失败，请重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n  const handlePreview = async file => {\n    if (!file.url && !file.preview) {\n      file.preview = await getBase64(file.originFileObj);\n    }\n    setPreviewImage(file.url || file.preview || '');\n    setPreviewVisible(true);\n  };\n  const getBase64 = file => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result);\n    reader.onerror = error => reject(error);\n  });\n  const startCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: 640,\n          height: 480\n        }\n      });\n      if (cameraRef.current) {\n        cameraRef.current.srcObject = stream;\n        setIsCameraActive(true);\n        message.success('摄像头已启动');\n      }\n    } catch (error) {\n      message.error('无法访问摄像头，请检查权限设置');\n    }\n  };\n  const stopCamera = () => {\n    var _cameraRef$current;\n    if ((_cameraRef$current = cameraRef.current) !== null && _cameraRef$current !== void 0 && _cameraRef$current.srcObject) {\n      const stream = cameraRef.current.srcObject;\n      stream.getTracks().forEach(track => track.stop());\n      cameraRef.current.srcObject = null;\n      setIsCameraActive(false);\n      message.info('摄像头已关闭');\n    }\n  };\n  const capturePhoto = () => {\n    if (cameraRef.current && canvasRef.current) {\n      const canvas = canvasRef.current;\n      const video = cameraRef.current;\n      const context = canvas.getContext('2d');\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      context === null || context === void 0 ? void 0 : context.drawImage(video, 0, 0);\n      canvas.toBlob(blob => {\n        if (blob) {\n          const file = new File([blob], `capture_${Date.now()}.jpg`, {\n            type: 'image/jpeg'\n          });\n          handleUpload({\n            file,\n            onSuccess: () => {},\n            onError: () => {}\n          });\n        }\n      });\n    }\n  };\n  const clearAll = () => {\n    setAnalyses([]);\n    setSelectedImage(null);\n    setFileList([]);\n    message.success('已清空所有分析结果');\n  };\n  const getTypeIcon = type => {\n    const typeConfig = recognitionTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.icon) || /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 32\n    }, this);\n  };\n  const getTypeColor = type => {\n    const typeConfig = recognitionTypes.find(t => t.value === type);\n    return (typeConfig === null || typeConfig === void 0 ? void 0 : typeConfig.color) || '#1890ff';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(-1),\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u89C6\\u89C9\\u8BC6\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            defaultActiveKey: \"upload\",\n            children: [/*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n              tab: \"\\u6587\\u4EF6\\u4E0A\\u4F20\",\n              children: [/*#__PURE__*/_jsxDEV(Upload.Dragger, {\n                name: \"file\",\n                multiple: true,\n                accept: \"image/*\",\n                customRequest: handleUpload,\n                onPreview: handlePreview,\n                fileList: fileList,\n                onChange: ({\n                  fileList\n                }) => setFileList(fileList),\n                disabled: isAnalyzing,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-drag-icon\",\n                  children: /*#__PURE__*/_jsxDEV(UploadOutlined, {\n                    style: {\n                      fontSize: 48,\n                      color: '#1890ff'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-text\",\n                  children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u56FE\\u7247\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ant-upload-hint\",\n                  children: \"\\u652F\\u6301 JPG\\u3001PNG\\u3001GIF \\u7B49\\u683C\\u5F0F\\uFF0C\\u5355\\u4E2A\\u6587\\u4EF6\\u4E0D\\u8D85\\u8FC7 10MB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Progress, {\n                  type: \"circle\",\n                  percent: 75\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u6B63\\u5728\\u5206\\u6790\\u56FE\\u7247\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, \"upload\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tabs.TabPane, {\n              tab: \"\\u6444\\u50CF\\u5934\\u62CD\\u7167\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 16\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: cameraRef,\n                    autoPlay: true,\n                    playsInline: true,\n                    style: {\n                      width: '100%',\n                      maxWidth: 640,\n                      height: 'auto',\n                      border: '1px solid #d9d9d9',\n                      borderRadius: 6,\n                      display: isCameraActive ? 'block' : 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                      display: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this), !isCameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100%',\n                      height: 300,\n                      border: '1px dashed #d9d9d9',\n                      borderRadius: 6,\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexDirection: 'column',\n                      color: '#999'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(CameraOutlined, {\n                      style: {\n                        fontSize: 48,\n                        marginBottom: 16\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u70B9\\u51FB\\u4E0B\\u65B9\\u6309\\u94AE\\u542F\\u52A8\\u6444\\u50CF\\u5934\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Space, {\n                  children: !isCameraActive ? /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 31\n                    }, this),\n                    onClick: startCamera,\n                    children: \"\\u542F\\u52A8\\u6444\\u50CF\\u5934\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 33\n                      }, this),\n                      onClick: capturePhoto,\n                      loading: isAnalyzing,\n                      children: \"\\u62CD\\u7167\\u8BC6\\u522B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 33\n                      }, this),\n                      onClick: stopCamera,\n                      children: \"\\u5173\\u95ED\\u6444\\u50CF\\u5934\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)\n            }, \"camera\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BC6\\u522B\\u8BBE\\u7F6E\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u8BC6\\u522B\\u7C7B\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                mode: \"multiple\",\n                value: selectedTypes,\n                onChange: setSelectedTypes,\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                placeholder: \"\\u9009\\u62E9\\u8BC6\\u522B\\u7C7B\\u578B\",\n                children: recognitionTypes.map(type => /*#__PURE__*/_jsxDEV(Option, {\n                  value: type.value,\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [type.icon, type.label]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 23\n                  }, this)\n                }, type.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u7F6E\\u4FE1\\u5EA6\\u9608\\u503C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: confidence,\n                onChange: setConfidence,\n                style: {\n                  marginTop: 8\n                },\n                tooltip: {\n                  formatter: value => `${value}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: 12\n                },\n                children: [\"\\u5F53\\u524D: \", confidence, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                style: {\n                  width: '100%',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u5B9E\\u65F6\\u5206\\u6790\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: enableRealtime,\n                  onChange: setEnableRealtime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6700\\u5927\\u7ED3\\u679C\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: maxResults,\n                onChange: setMaxResults,\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 5,\n                  children: \"5\\u4E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 10,\n                  children: \"10\\u4E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 20,\n                  children: \"20\\u4E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 50,\n                  children: \"50\\u4E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7EDF\\u8BA1\\u4FE1\\u606F\",\n          size: \"small\",\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5DF2\\u5206\\u6790\",\n                value: analyses.length,\n                prefix: /*#__PURE__*/_jsxDEV(PictureOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 27\n                }, this),\n                suffix: \"\\u5F20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u8BC6\\u522B\\u5BF9\\u8C61\",\n                value: analyses.reduce((sum, a) => sum + a.results.length, 0),\n                prefix: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 27\n                }, this),\n                suffix: \"\\u4E2A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u5206\\u6790\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n          count: analyses.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 21\n          }, this),\n          onClick: () => message.info('导出功能开发中'),\n          children: \"\\u5BFC\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 21\n          }, this),\n          onClick: clearAll,\n          disabled: analyses.length === 0,\n          children: \"\\u6E05\\u7A7A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 11\n      }, this),\n      style: {\n        marginTop: 24\n      },\n      children: analyses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0',\n          color: '#999'\n        },\n        children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {\n          style: {\n            fontSize: 48,\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u6682\\u65E0\\u5206\\u6790\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12\n          },\n          children: \"\\u4E0A\\u4F20\\u56FE\\u7247\\u6216\\u4F7F\\u7528\\u6444\\u50CF\\u5934\\u62CD\\u7167\\u6765\\u5F00\\u59CB\\u5206\\u6790\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: analyses,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                cursor: 'pointer',\n                backgroundColor: (selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.id) === item.id ? '#f0f0f0' : 'transparent',\n                padding: '8px 12px',\n                borderRadius: 4\n              },\n              onClick: () => setSelectedImage(item),\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      fontSize: 12\n                    },\n                    children: item.filename\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    count: item.results.length,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 25\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 11\n                    },\n                    children: item.timestamp\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 11\n                    },\n                    children: [\"\\u5904\\u7406\\u65F6\\u95F4: \", item.processingTime.toFixed(1), \"s\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 16,\n          children: selectedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16,\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Image, {\n                src: selectedImage.url,\n                alt: selectedImage.filename,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 400\n                },\n                preview: {\n                  mask: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(ZoomInOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 29\n                    }, this), \"\\u9884\\u89C8\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 27\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"left\",\n              children: \"\\u8BC6\\u522B\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dataSource: selectedImage.results,\n              renderItem: result => {\n                var _recognitionTypes$fin;\n                return /*#__PURE__*/_jsxDEV(List.Item, {\n                  children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                    avatar: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: 32,\n                        height: 32,\n                        borderRadius: '50%',\n                        backgroundColor: getTypeColor(result.type),\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: 'white'\n                      },\n                      children: getTypeIcon(result.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 29\n                    }, this),\n                    title: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        strong: true,\n                        children: result.label\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 578,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                        color: getTypeColor(result.type),\n                        children: (_recognitionTypes$fin = recognitionTypes.find(t => t.value === result.type)) === null || _recognitionTypes$fin === void 0 ? void 0 : _recognitionTypes$fin.label\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                        color: result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red',\n                        children: [(result.confidence * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 582,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 29\n                    }, this),\n                    description: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [result.bbox && /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        style: {\n                          fontSize: 12\n                        },\n                        children: [\"\\u4F4D\\u7F6E: (\", result.bbox.x, \", \", result.bbox.y, \") \\u5C3A\\u5BF8: \", result.bbox.width, \"\\xD7\", result.bbox.height]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 33\n                      }, this), result.attributes && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginTop: 4\n                        },\n                        children: Object.entries(result.attributes).map(([key, value]) => /*#__PURE__*/_jsxDEV(Tag, {\n                          children: [key, \": \", value]\n                        }, key, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 598,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 23\n                }, this);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: previewVisible,\n      title: \"\\u56FE\\u7247\\u9884\\u89C8\",\n      footer: null,\n      onCancel: () => setPreviewVisible(false),\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        alt: \"preview\",\n        style: {\n          width: '100%'\n        },\n        src: previewImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n};\n_s(VisionRecognition, \"ifQ7HWIy/hF5Zimmt47XhG0C/uY=\", false, function () {\n  return [useNavigate];\n});\n_c = VisionRecognition;\nexport default VisionRecognition;\nvar _c;\n$RefreshReg$(_c, \"VisionRecognition\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Typography", "Card", "<PERSON><PERSON>", "Space", "Row", "Col", "Upload", "Image", "List", "Tag", "Progress", "Tabs", "Select", "Switch", "Slide<PERSON>", "message", "Modal", "Divider", "Statistic", "Badge", "ArrowLeftOutlined", "EyeOutlined", "UploadOutlined", "CameraOutlined", "DeleteOutlined", "DownloadOutlined", "ZoomInOutlined", "ClearOutlined", "ScanOutlined", "PictureOutlined", "UserOutlined", "CarOutlined", "HomeOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Paragraph", "Text", "Option", "VisionRecognition", "_s", "navigate", "fileList", "setFileList", "analyses", "setAnalyses", "selectedImage", "setSelectedImage", "isAnalyzing", "setIsAnalyzing", "previewVisible", "setPreviewVisible", "previewImage", "setPreviewImage", "selectedTypes", "setSelectedTypes", "confidence", "setConfidence", "enableRealtime", "setEnableRealtime", "maxResults", "setMaxResults", "cameraRef", "canvasRef", "isCameraActive", "setIsCameraActive", "recognitionTypes", "value", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mockResults", "id", "type", "bbox", "x", "y", "width", "height", "attributes", "brand", "age", "gender", "emotion", "handleUpload", "options", "file", "onSuccess", "onError", "Promise", "resolve", "setTimeout", "imageUrl", "URL", "createObjectURL", "newAnalysis", "Date", "now", "toString", "filename", "name", "url", "size", "dimensions", "results", "filter", "r", "includes", "timestamp", "toLocaleString", "processingTime", "Math", "random", "prev", "success", "error", "handlePreview", "preview", "getBase64", "originFileObj", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "startCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "current", "srcObject", "stopCamera", "_cameraRef$current", "getTracks", "for<PERSON>ach", "track", "stop", "info", "capturePhoto", "canvas", "context", "getContext", "videoWidth", "videoHeight", "drawImage", "toBlob", "blob", "File", "clearAll", "getTypeIcon", "typeConfig", "find", "t", "getTypeColor", "children", "style", "marginBottom", "onClick", "gutter", "span", "title", "defaultActiveKey", "TabPane", "tab", "<PERSON><PERSON>", "multiple", "accept", "customRequest", "onPreview", "onChange", "disabled", "className", "fontSize", "marginTop", "textAlign", "percent", "ref", "autoPlay", "playsInline", "max<PERSON><PERSON><PERSON>", "border", "borderRadius", "display", "alignItems", "justifyContent", "flexDirection", "loading", "direction", "strong", "mode", "placeholder", "map", "tooltip", "formatter", "checked", "length", "prefix", "suffix", "reduce", "sum", "a", "count", "extra", "padding", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "cursor", "backgroundColor", "Meta", "description", "toFixed", "src", "alt", "maxHeight", "mask", "orientation", "_recognitionTypes$fin", "avatar", "Object", "entries", "key", "open", "footer", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Recognition/VisionRecognition.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Button,\n  Space,\n  Row,\n  Col,\n  Upload,\n  Image,\n  List,\n  Tag,\n  Progress,\n  Alert,\n  Tabs,\n  Select,\n  Switch,\n  Slider,\n  message,\n  Modal,\n  Divider,\n  Statistic,\n  Badge,\n  Tooltip,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  EyeOutlined,\n  UploadOutlined,\n  CameraOutlined,\n  DeleteOutlined,\n  DownloadOutlined,\n  ZoomInOutlined,\n  ZoomOutOutlined,\n  RotateLeftOutlined,\n  RotateRightOutlined,\n  ClearOutlined,\n  ScanOutlined,\n  PictureOutlined,\n  UserOutlined,\n  CarOutlined,\n  HomeOutlined,\n  ShoppingOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport type { UploadFile, UploadProps } from 'antd/es/upload/interface';\n\nconst { Title, Paragraph, Text } = Typography;\nconst { Option } = Select;\n\ninterface RecognitionResult {\n  id: string;\n  type: 'object' | 'face' | 'text' | 'scene' | 'landmark';\n  label: string;\n  confidence: number;\n  bbox?: {\n    x: number;\n    y: number;\n    width: number;\n    height: number;\n  };\n  attributes?: Record<string, any>;\n}\n\ninterface ImageAnalysis {\n  id: string;\n  filename: string;\n  url: string;\n  size: number;\n  dimensions: {\n    width: number;\n    height: number;\n  };\n  results: RecognitionResult[];\n  timestamp: string;\n  processingTime: number;\n}\n\nconst VisionRecognition: React.FC = () => {\n  const navigate = useNavigate();\n  const [fileList, setFileList] = useState<UploadFile[]>([]);\n  const [analyses, setAnalyses] = useState<ImageAnalysis[]>([]);\n  const [selectedImage, setSelectedImage] = useState<ImageAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [selectedTypes, setSelectedTypes] = useState<string[]>(['object', 'face', 'text', 'scene']);\n  const [confidence, setConfidence] = useState(70);\n  const [enableRealtime, setEnableRealtime] = useState(true);\n  const [maxResults, setMaxResults] = useState(10);\n\n  const cameraRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n\n  // 识别类型配置\n  const recognitionTypes = [\n    { value: 'object', label: '物体识别', icon: <ScanOutlined />, color: '#1890ff' },\n    { value: 'face', label: '人脸识别', icon: <UserOutlined />, color: '#52c41a' },\n    { value: 'text', label: '文字识别', icon: <PictureOutlined />, color: '#faad14' },\n    { value: 'scene', label: '场景识别', icon: <HomeOutlined />, color: '#722ed1' },\n    { value: 'landmark', label: '地标识别', icon: <CarOutlined />, color: '#eb2f96' },\n  ];\n\n  // 模拟识别结果\n  const mockResults: RecognitionResult[] = [\n    {\n      id: '1',\n      type: 'object',\n      label: '汽车',\n      confidence: 0.95,\n      bbox: { x: 100, y: 50, width: 200, height: 150 },\n      attributes: { color: '红色', brand: '丰田' },\n    },\n    {\n      id: '2',\n      type: 'face',\n      label: '人脸',\n      confidence: 0.88,\n      bbox: { x: 300, y: 80, width: 80, height: 100 },\n      attributes: { age: '25-35', gender: '女性', emotion: '微笑' },\n    },\n    {\n      id: '3',\n      type: 'text',\n      label: '停车场',\n      confidence: 0.92,\n      bbox: { x: 50, y: 20, width: 120, height: 30 },\n    },\n    {\n      id: '4',\n      type: 'scene',\n      label: '城市街道',\n      confidence: 0.85,\n    },\n  ];\n\n  const handleUpload: UploadProps['customRequest'] = async (options) => {\n    const { file, onSuccess, onError } = options;\n\n    try {\n      setIsAnalyzing(true);\n\n      // 模拟文件上传和分析\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      const imageUrl = URL.createObjectURL(file as File);\n      const newAnalysis: ImageAnalysis = {\n        id: Date.now().toString(),\n        filename: (file as File).name,\n        url: imageUrl,\n        size: (file as File).size,\n        dimensions: { width: 800, height: 600 }, // 模拟尺寸\n        results: mockResults.filter(r => selectedTypes.includes(r.type)),\n        timestamp: new Date().toLocaleString(),\n        processingTime: 1.5 + Math.random() * 2,\n      };\n\n      setAnalyses(prev => [newAnalysis, ...prev]);\n      setSelectedImage(newAnalysis);\n\n      onSuccess?.(newAnalysis);\n      message.success(`${(file as File).name} 分析完成`);\n    } catch (error) {\n      onError?.(error as Error);\n      message.error('分析失败，请重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const handlePreview = async (file: UploadFile) => {\n    if (!file.url && !file.preview) {\n      file.preview = await getBase64(file.originFileObj as File);\n    }\n    setPreviewImage(file.url || file.preview || '');\n    setPreviewVisible(true);\n  };\n\n  const getBase64 = (file: File): Promise<string> =>\n    new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result as string);\n      reader.onerror = error => reject(error);\n    });\n\n  const startCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { width: 640, height: 480 }\n      });\n      if (cameraRef.current) {\n        cameraRef.current.srcObject = stream;\n        setIsCameraActive(true);\n        message.success('摄像头已启动');\n      }\n    } catch (error) {\n      message.error('无法访问摄像头，请检查权限设置');\n    }\n  };\n\n  const stopCamera = () => {\n    if (cameraRef.current?.srcObject) {\n      const stream = cameraRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      cameraRef.current.srcObject = null;\n      setIsCameraActive(false);\n      message.info('摄像头已关闭');\n    }\n  };\n\n  const capturePhoto = () => {\n    if (cameraRef.current && canvasRef.current) {\n      const canvas = canvasRef.current;\n      const video = cameraRef.current;\n      const context = canvas.getContext('2d');\n\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      context?.drawImage(video, 0, 0);\n\n      canvas.toBlob(blob => {\n        if (blob) {\n          const file = new File([blob], `capture_${Date.now()}.jpg`, { type: 'image/jpeg' });\n          handleUpload({\n            file,\n            onSuccess: () => {},\n            onError: () => {},\n          } as any);\n        }\n      });\n    }\n  };\n\n  const clearAll = () => {\n    setAnalyses([]);\n    setSelectedImage(null);\n    setFileList([]);\n    message.success('已清空所有分析结果');\n  };\n\n  const getTypeIcon = (type: string) => {\n    const typeConfig = recognitionTypes.find(t => t.value === type);\n    return typeConfig?.icon || <ScanOutlined />;\n  };\n\n  const getTypeColor = (type: string) => {\n    const typeConfig = recognitionTypes.find(t => t.value === type);\n    return typeConfig?.color || '#1890ff';\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(-1)}\n          >\n            返回\n          </Button>\n        </Space>\n      </div>\n\n      <Row gutter={24}>\n        <Col span={16}>\n          <Card title={\n            <Space>\n              <EyeOutlined />\n              <span>视觉识别</span>\n            </Space>\n          }>\n            <Tabs defaultActiveKey=\"upload\">\n              <Tabs.TabPane tab=\"文件上传\" key=\"upload\">\n                <Upload.Dragger\n                  name=\"file\"\n                  multiple\n                  accept=\"image/*\"\n                  customRequest={handleUpload}\n                  onPreview={handlePreview}\n                  fileList={fileList}\n                  onChange={({ fileList }) => setFileList(fileList)}\n                  disabled={isAnalyzing}\n                >\n                  <p className=\"ant-upload-drag-icon\">\n                    <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n                  </p>\n                  <p className=\"ant-upload-text\">点击或拖拽图片到此区域上传</p>\n                  <p className=\"ant-upload-hint\">\n                    支持 JPG、PNG、GIF 等格式，单个文件不超过 10MB\n                  </p>\n                </Upload.Dragger>\n\n                {isAnalyzing && (\n                  <div style={{ marginTop: 16, textAlign: 'center' }}>\n                    <Progress type=\"circle\" percent={75} />\n                    <div style={{ marginTop: 8 }}>\n                      <Text>正在分析图片，请稍候...</Text>\n                    </div>\n                  </div>\n                )}\n              </Tabs.TabPane>\n\n              <Tabs.TabPane tab=\"摄像头拍照\" key=\"camera\">\n                <div style={{ textAlign: 'center' }}>\n                  <div style={{ marginBottom: 16 }}>\n                    <video\n                      ref={cameraRef}\n                      autoPlay\n                      playsInline\n                      style={{\n                        width: '100%',\n                        maxWidth: 640,\n                        height: 'auto',\n                        border: '1px solid #d9d9d9',\n                        borderRadius: 6,\n                        display: isCameraActive ? 'block' : 'none',\n                      }}\n                    />\n                    <canvas ref={canvasRef} style={{ display: 'none' }} />\n\n                    {!isCameraActive && (\n                      <div style={{\n                        width: '100%',\n                        height: 300,\n                        border: '1px dashed #d9d9d9',\n                        borderRadius: 6,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        flexDirection: 'column',\n                        color: '#999',\n                      }}>\n                        <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />\n                        <Text type=\"secondary\">点击下方按钮启动摄像头</Text>\n                      </div>\n                    )}\n                  </div>\n\n                  <Space>\n                    {!isCameraActive ? (\n                      <Button\n                        type=\"primary\"\n                        icon={<CameraOutlined />}\n                        onClick={startCamera}\n                      >\n                        启动摄像头\n                      </Button>\n                    ) : (\n                      <>\n                        <Button\n                          type=\"primary\"\n                          icon={<CameraOutlined />}\n                          onClick={capturePhoto}\n                          loading={isAnalyzing}\n                        >\n                          拍照识别\n                        </Button>\n                        <Button\n                          icon={<DeleteOutlined />}\n                          onClick={stopCamera}\n                        >\n                          关闭摄像头\n                        </Button>\n                      </>\n                    )}\n                  </Space>\n                </div>\n              </Tabs.TabPane>\n            </Tabs>\n          </Card>\n        </Col>\n\n        <Col span={8}>\n          <Card title=\"识别设置\" size=\"small\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>识别类型</Text>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedTypes}\n                  onChange={setSelectedTypes}\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择识别类型\"\n                >\n                  {recognitionTypes.map(type => (\n                    <Option key={type.value} value={type.value}>\n                      <Space>\n                        {type.icon}\n                        {type.label}\n                      </Space>\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              <div>\n                <Text strong>置信度阈值</Text>\n                <Slider\n                  value={confidence}\n                  onChange={setConfidence}\n                  style={{ marginTop: 8 }}\n                  tooltip={{ formatter: (value) => `${value}%` }}\n                />\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                  当前: {confidence}%\n                </Text>\n              </div>\n\n              <div>\n                <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n                  <Text strong>实时分析</Text>\n                  <Switch\n                    checked={enableRealtime}\n                    onChange={setEnableRealtime}\n                  />\n                </Space>\n              </div>\n\n              <div>\n                <Text strong>最大结果数</Text>\n                <Select\n                  value={maxResults}\n                  onChange={setMaxResults}\n                  style={{ width: '100%', marginTop: 8 }}\n                >\n                  <Option value={5}>5个</Option>\n                  <Option value={10}>10个</Option>\n                  <Option value={20}>20个</Option>\n                  <Option value={50}>50个</Option>\n                </Select>\n              </div>\n            </Space>\n          </Card>\n\n          <Card title=\"统计信息\" size=\"small\" style={{ marginTop: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic\n                  title=\"已分析\"\n                  value={analyses.length}\n                  prefix={<PictureOutlined />}\n                  suffix=\"张\"\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"识别对象\"\n                  value={analyses.reduce((sum, a) => sum + a.results.length, 0)}\n                  prefix={<ScanOutlined />}\n                  suffix=\"个\"\n                />\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 分析结果 */}\n      <Card\n        title={\n          <Space>\n            <EyeOutlined />\n            <span>分析结果</span>\n            <Badge count={analyses.length} />\n          </Space>\n        }\n        extra={\n          <Space>\n            <Button\n              icon={<DownloadOutlined />}\n              onClick={() => message.info('导出功能开发中')}\n            >\n              导出\n            </Button>\n            <Button\n              icon={<ClearOutlined />}\n              onClick={clearAll}\n              disabled={analyses.length === 0}\n            >\n              清空\n            </Button>\n          </Space>\n        }\n        style={{ marginTop: 24 }}\n      >\n        {analyses.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>\n            <EyeOutlined style={{ fontSize: 48, marginBottom: 16 }} />\n            <div>暂无分析结果</div>\n            <div style={{ fontSize: 12 }}>上传图片或使用摄像头拍照来开始分析</div>\n          </div>\n        ) : (\n          <Row gutter={16}>\n            <Col span={8}>\n              <List\n                size=\"small\"\n                dataSource={analyses}\n                renderItem={(item) => (\n                  <List.Item\n                    style={{\n                      cursor: 'pointer',\n                      backgroundColor: selectedImage?.id === item.id ? '#f0f0f0' : 'transparent',\n                      padding: '8px 12px',\n                      borderRadius: 4,\n                    }}\n                    onClick={() => setSelectedImage(item)}\n                  >\n                    <List.Item.Meta\n                      title={\n                        <Space>\n                          <Text strong style={{ fontSize: 12 }}>\n                            {item.filename}\n                          </Text>\n                          <Badge count={item.results.length} size=\"small\" />\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                            {item.timestamp}\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                            处理时间: {item.processingTime.toFixed(1)}s\n                          </Text>\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Col>\n\n            <Col span={16}>\n              {selectedImage && (\n                <div>\n                  <div style={{ marginBottom: 16, textAlign: 'center' }}>\n                    <Image\n                      src={selectedImage.url}\n                      alt={selectedImage.filename}\n                      style={{ maxWidth: '100%', maxHeight: 400 }}\n                      preview={{\n                        mask: (\n                          <Space>\n                            <ZoomInOutlined />\n                            预览\n                          </Space>\n                        ),\n                      }}\n                    />\n                  </div>\n\n                  <Divider orientation=\"left\">识别结果</Divider>\n\n                  <List\n                    dataSource={selectedImage.results}\n                    renderItem={(result) => (\n                      <List.Item>\n                        <List.Item.Meta\n                          avatar={\n                            <div style={{\n                              width: 32,\n                              height: 32,\n                              borderRadius: '50%',\n                              backgroundColor: getTypeColor(result.type),\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              color: 'white',\n                            }}>\n                              {getTypeIcon(result.type)}\n                            </div>\n                          }\n                          title={\n                            <Space>\n                              <Text strong>{result.label}</Text>\n                              <Tag color={getTypeColor(result.type)}>\n                                {recognitionTypes.find(t => t.value === result.type)?.label}\n                              </Tag>\n                              <Tag color={result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red'}>\n                                {(result.confidence * 100).toFixed(1)}%\n                              </Tag>\n                            </Space>\n                          }\n                          description={\n                            <div>\n                              {result.bbox && (\n                                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                                  位置: ({result.bbox.x}, {result.bbox.y})\n                                  尺寸: {result.bbox.width}×{result.bbox.height}\n                                </Text>\n                              )}\n                              {result.attributes && (\n                                <div style={{ marginTop: 4 }}>\n                                  {Object.entries(result.attributes).map(([key, value]) => (\n                                    <Tag key={key}>\n                                      {key}: {value}\n                                    </Tag>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          }\n                        />\n                      </List.Item>\n                    )}\n                  />\n                </div>\n              )}\n            </Col>\n          </Row>\n        )}\n      </Card>\n\n      {/* 预览模态框 */}\n      <Modal\n        open={previewVisible}\n        title=\"图片预览\"\n        footer={null}\n        onCancel={() => setPreviewVisible(false)}\n        width={800}\n      >\n        <img alt=\"preview\" style={{ width: '100%' }} src={previewImage} />\n      </Modal>\n    </div>\n  );\n};\n\nexport default VisionRecognition;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAqB,OAAO;AAC5D,SACEC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,QAAQ,EAERC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,SAAS,EACTC,KAAK,QAEA,MAAM;AACb,SACEC,iBAAiB,EACjBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EAIdC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,YAAY,EACZC,WAAW,EACXC,YAAY,QAEP,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG/C,MAAM;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAGxC,UAAU;AAC7C,MAAM;EAAEyC;AAAO,CAAC,GAAG7B,MAAM;AA8BzB,MAAM8B,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAkB,EAAE,CAAC;EAC7D,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAuB,IAAI,CAAC;EAC9E,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EACjG,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMmE,SAAS,GAAGlE,MAAM,CAAmB,IAAI,CAAC;EAChD,MAAMmE,SAAS,GAAGnE,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAM,CAACoE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMuE,gBAAgB,GAAG,CACvB;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAErC,OAAA,CAACP,YAAY;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5E;IAAEP,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAErC,OAAA,CAACL,YAAY;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC1E;IAAEP,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAErC,OAAA,CAACN,eAAe;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7E;IAAEP,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAErC,OAAA,CAACH,YAAY;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3E;IAAEP,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAErC,OAAA,CAACJ,WAAW;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC9E;;EAED;EACA,MAAMC,WAAgC,GAAG,CACvC;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,QAAQ;IACdT,KAAK,EAAE,IAAI;IACXZ,UAAU,EAAE,IAAI;IAChBsB,IAAI,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC;IAChDC,UAAU,EAAE;MAAET,KAAK,EAAE,IAAI;MAAEU,KAAK,EAAE;IAAK;EACzC,CAAC,EACD;IACER,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,MAAM;IACZT,KAAK,EAAE,IAAI;IACXZ,UAAU,EAAE,IAAI;IAChBsB,IAAI,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAI,CAAC;IAC/CC,UAAU,EAAE;MAAEE,GAAG,EAAE,OAAO;MAAEC,MAAM,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAK;EAC1D,CAAC,EACD;IACEX,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,MAAM;IACZT,KAAK,EAAE,KAAK;IACZZ,UAAU,EAAE,IAAI;IAChBsB,IAAI,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG;EAC/C,CAAC,EACD;IACEN,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,OAAO;IACbT,KAAK,EAAE,MAAM;IACbZ,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMgC,YAA0C,GAAG,MAAOC,OAAO,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAGH,OAAO;IAE5C,IAAI;MACFxC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM,IAAI4C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAY,CAAC;MAClD,MAAMS,WAA0B,GAAG;QACjCvB,EAAE,EAAEwB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,QAAQ,EAAGb,IAAI,CAAUc,IAAI;QAC7BC,GAAG,EAAET,QAAQ;QACbU,IAAI,EAAGhB,IAAI,CAAUgB,IAAI;QACzBC,UAAU,EAAE;UAAE1B,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI,CAAC;QAAE;QACzC0B,OAAO,EAAEjC,WAAW,CAACkC,MAAM,CAACC,CAAC,IAAIxD,aAAa,CAACyD,QAAQ,CAACD,CAAC,CAACjC,IAAI,CAAC,CAAC;QAChEmC,SAAS,EAAE,IAAIZ,IAAI,CAAC,CAAC,CAACa,cAAc,CAAC,CAAC;QACtCC,cAAc,EAAE,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MACxC,CAAC;MAEDvE,WAAW,CAACwE,IAAI,IAAI,CAAClB,WAAW,EAAE,GAAGkB,IAAI,CAAC,CAAC;MAC3CtE,gBAAgB,CAACoD,WAAW,CAAC;MAE7BR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,CAAC;MACxBvF,OAAO,CAAC0G,OAAO,CAAC,GAAI5B,IAAI,CAAUc,IAAI,OAAO,CAAC;IAChD,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd3B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG2B,KAAc,CAAC;MACzB3G,OAAO,CAAC2G,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRtE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMuE,aAAa,GAAG,MAAO9B,IAAgB,IAAK;IAChD,IAAI,CAACA,IAAI,CAACe,GAAG,IAAI,CAACf,IAAI,CAAC+B,OAAO,EAAE;MAC9B/B,IAAI,CAAC+B,OAAO,GAAG,MAAMC,SAAS,CAAChC,IAAI,CAACiC,aAAqB,CAAC;IAC5D;IACAtE,eAAe,CAACqC,IAAI,CAACe,GAAG,IAAIf,IAAI,CAAC+B,OAAO,IAAI,EAAE,CAAC;IAC/CtE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMuE,SAAS,GAAIhC,IAAU,IAC3B,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAE8B,MAAM,KAAK;IAC/B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,aAAa,CAACrC,IAAI,CAAC;IAC1BmC,MAAM,CAACG,MAAM,GAAG,MAAMlC,OAAO,CAAC+B,MAAM,CAACI,MAAgB,CAAC;IACtDJ,MAAM,CAACK,OAAO,GAAGX,KAAK,IAAIK,MAAM,CAACL,KAAK,CAAC;EACzC,CAAC,CAAC;EAEJ,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UAAEvD,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI;MACnC,CAAC,CAAC;MACF,IAAIpB,SAAS,CAAC2E,OAAO,EAAE;QACrB3E,SAAS,CAAC2E,OAAO,CAACC,SAAS,GAAGN,MAAM;QACpCnE,iBAAiB,CAAC,IAAI,CAAC;QACvBrD,OAAO,CAAC0G,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3G,OAAO,CAAC2G,KAAK,CAAC,iBAAiB,CAAC;IAClC;EACF,CAAC;EAED,MAAMoB,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA;IACvB,KAAAA,kBAAA,GAAI9E,SAAS,CAAC2E,OAAO,cAAAG,kBAAA,eAAjBA,kBAAA,CAAmBF,SAAS,EAAE;MAChC,MAAMN,MAAM,GAAGtE,SAAS,CAAC2E,OAAO,CAACC,SAAwB;MACzDN,MAAM,CAACS,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACjDlF,SAAS,CAAC2E,OAAO,CAACC,SAAS,GAAG,IAAI;MAClCzE,iBAAiB,CAAC,KAAK,CAAC;MACxBrD,OAAO,CAACqI,IAAI,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIpF,SAAS,CAAC2E,OAAO,IAAI1E,SAAS,CAAC0E,OAAO,EAAE;MAC1C,MAAMU,MAAM,GAAGpF,SAAS,CAAC0E,OAAO;MAChC,MAAMD,KAAK,GAAG1E,SAAS,CAAC2E,OAAO;MAC/B,MAAMW,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MAEvCF,MAAM,CAAClE,KAAK,GAAGuD,KAAK,CAACc,UAAU;MAC/BH,MAAM,CAACjE,MAAM,GAAGsD,KAAK,CAACe,WAAW;MACjCH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,SAAS,CAAChB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAE/BW,MAAM,CAACM,MAAM,CAACC,IAAI,IAAI;QACpB,IAAIA,IAAI,EAAE;UACR,MAAMhE,IAAI,GAAG,IAAIiE,IAAI,CAAC,CAACD,IAAI,CAAC,EAAE,WAAWtD,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM,EAAE;YAAExB,IAAI,EAAE;UAAa,CAAC,CAAC;UAClFW,YAAY,CAAC;YACXE,IAAI;YACJC,SAAS,EAAEA,CAAA,KAAM,CAAC,CAAC;YACnBC,OAAO,EAAEA,CAAA,KAAM,CAAC;UAClB,CAAQ,CAAC;QACX;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMgE,QAAQ,GAAGA,CAAA,KAAM;IACrB/G,WAAW,CAAC,EAAE,CAAC;IACfE,gBAAgB,CAAC,IAAI,CAAC;IACtBJ,WAAW,CAAC,EAAE,CAAC;IACf/B,OAAO,CAAC0G,OAAO,CAAC,WAAW,CAAC;EAC9B,CAAC;EAED,MAAMuC,WAAW,GAAIhF,IAAY,IAAK;IACpC,MAAMiF,UAAU,GAAG5F,gBAAgB,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7F,KAAK,KAAKU,IAAI,CAAC;IAC/D,OAAO,CAAAiF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzF,IAAI,kBAAIrC,OAAA,CAACP,YAAY;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C,CAAC;EAED,MAAMwF,YAAY,GAAIpF,IAAY,IAAK;IACrC,MAAMiF,UAAU,GAAG5F,gBAAgB,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7F,KAAK,KAAKU,IAAI,CAAC;IAC/D,OAAO,CAAAiF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpF,KAAK,KAAI,SAAS;EACvC,CAAC;EAED,oBACE1C,OAAA;IAAAkI,QAAA,gBACElI,OAAA;MAAKmI,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAF,QAAA,eAC/BlI,OAAA,CAAChC,KAAK;QAAAkK,QAAA,eACJlI,OAAA,CAACjC,MAAM;UACLsE,IAAI,eAAErC,OAAA,CAACf,iBAAiB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5B4F,OAAO,EAAEA,CAAA,KAAM5H,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAAyH,QAAA,EAC7B;QAED;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENzC,OAAA,CAAC/B,GAAG;MAACqK,MAAM,EAAE,EAAG;MAAAJ,QAAA,gBACdlI,OAAA,CAAC9B,GAAG;QAACqK,IAAI,EAAE,EAAG;QAAAL,QAAA,eACZlI,OAAA,CAAClC,IAAI;UAAC0K,KAAK,eACTxI,OAAA,CAAChC,KAAK;YAAAkK,QAAA,gBACJlI,OAAA,CAACd,WAAW;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfzC,OAAA;cAAAkI,QAAA,EAAM;YAAI;cAAA5F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UAAAyF,QAAA,eACClI,OAAA,CAACxB,IAAI;YAACiK,gBAAgB,EAAC,QAAQ;YAAAP,QAAA,gBAC7BlI,OAAA,CAACxB,IAAI,CAACkK,OAAO;cAACC,GAAG,EAAC,0BAAM;cAAAT,QAAA,gBACtBlI,OAAA,CAAC7B,MAAM,CAACyK,OAAO;gBACbpE,IAAI,EAAC,MAAM;gBACXqE,QAAQ;gBACRC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAEvF,YAAa;gBAC5BwF,SAAS,EAAExD,aAAc;gBACzB9E,QAAQ,EAAEA,QAAS;gBACnBuI,QAAQ,EAAEA,CAAC;kBAAEvI;gBAAS,CAAC,KAAKC,WAAW,CAACD,QAAQ,CAAE;gBAClDwI,QAAQ,EAAElI,WAAY;gBAAAkH,QAAA,gBAEtBlI,OAAA;kBAAGmJ,SAAS,EAAC,sBAAsB;kBAAAjB,QAAA,eACjClI,OAAA,CAACb,cAAc;oBAACgJ,KAAK,EAAE;sBAAEiB,QAAQ,EAAE,EAAE;sBAAE1G,KAAK,EAAE;oBAAU;kBAAE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACJzC,OAAA;kBAAGmJ,SAAS,EAAC,iBAAiB;kBAAAjB,QAAA,EAAC;gBAAa;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChDzC,OAAA;kBAAGmJ,SAAS,EAAC,iBAAiB;kBAAAjB,QAAA,EAAC;gBAE/B;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,EAEhBzB,WAAW,iBACVhB,OAAA;gBAAKmI,KAAK,EAAE;kBAAEkB,SAAS,EAAE,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAApB,QAAA,gBACjDlI,OAAA,CAACzB,QAAQ;kBAACsE,IAAI,EAAC,QAAQ;kBAAC0G,OAAO,EAAE;gBAAG;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCzC,OAAA;kBAAKmI,KAAK,EAAE;oBAAEkB,SAAS,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,eAC3BlI,OAAA,CAACK,IAAI;oBAAA6H,QAAA,EAAC;kBAAa;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GA3B0B,QAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BvB,CAAC,eAEfzC,OAAA,CAACxB,IAAI,CAACkK,OAAO;cAACC,GAAG,EAAC,gCAAO;cAAAT,QAAA,eACvBlI,OAAA;gBAAKmI,KAAK,EAAE;kBAAEmB,SAAS,EAAE;gBAAS,CAAE;gBAAApB,QAAA,gBAClClI,OAAA;kBAAKmI,KAAK,EAAE;oBAAEC,YAAY,EAAE;kBAAG,CAAE;kBAAAF,QAAA,gBAC/BlI,OAAA;oBACEwJ,GAAG,EAAE1H,SAAU;oBACf2H,QAAQ;oBACRC,WAAW;oBACXvB,KAAK,EAAE;sBACLlF,KAAK,EAAE,MAAM;sBACb0G,QAAQ,EAAE,GAAG;sBACbzG,MAAM,EAAE,MAAM;sBACd0G,MAAM,EAAE,mBAAmB;sBAC3BC,YAAY,EAAE,CAAC;sBACfC,OAAO,EAAE9H,cAAc,GAAG,OAAO,GAAG;oBACtC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFzC,OAAA;oBAAQwJ,GAAG,EAAEzH,SAAU;oBAACoG,KAAK,EAAE;sBAAE2B,OAAO,EAAE;oBAAO;kBAAE;oBAAAxH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAErD,CAACT,cAAc,iBACdhC,OAAA;oBAAKmI,KAAK,EAAE;sBACVlF,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,GAAG;sBACX0G,MAAM,EAAE,oBAAoB;sBAC5BC,YAAY,EAAE,CAAC;sBACfC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBC,aAAa,EAAE,QAAQ;sBACvBvH,KAAK,EAAE;oBACT,CAAE;oBAAAwF,QAAA,gBACAlI,OAAA,CAACZ,cAAc;sBAAC+I,KAAK,EAAE;wBAAEiB,QAAQ,EAAE,EAAE;wBAAEhB,YAAY,EAAE;sBAAG;oBAAE;sBAAA9F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7DzC,OAAA,CAACK,IAAI;sBAACwC,IAAI,EAAC,WAAW;sBAAAqF,QAAA,EAAC;oBAAW;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENzC,OAAA,CAAChC,KAAK;kBAAAkK,QAAA,EACH,CAAClG,cAAc,gBACdhC,OAAA,CAACjC,MAAM;oBACL8E,IAAI,EAAC,SAAS;oBACdR,IAAI,eAAErC,OAAA,CAACZ,cAAc;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzB4F,OAAO,EAAElC,WAAY;oBAAA+B,QAAA,EACtB;kBAED;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAETzC,OAAA,CAAAE,SAAA;oBAAAgI,QAAA,gBACElI,OAAA,CAACjC,MAAM;sBACL8E,IAAI,EAAC,SAAS;sBACdR,IAAI,eAAErC,OAAA,CAACZ,cAAc;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzB4F,OAAO,EAAEnB,YAAa;sBACtBgD,OAAO,EAAElJ,WAAY;sBAAAkH,QAAA,EACtB;oBAED;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTzC,OAAA,CAACjC,MAAM;sBACLsE,IAAI,eAAErC,OAAA,CAACX,cAAc;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzB4F,OAAO,EAAE1B,UAAW;sBAAAuB,QAAA,EACrB;oBAED;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GAhEsB,QAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiExB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENzC,OAAA,CAAC9B,GAAG;QAACqK,IAAI,EAAE,CAAE;QAAAL,QAAA,gBACXlI,OAAA,CAAClC,IAAI;UAAC0K,KAAK,EAAC,0BAAM;UAAC9D,IAAI,EAAC,OAAO;UAAAwD,QAAA,eAC7BlI,OAAA,CAAChC,KAAK;YAACmM,SAAS,EAAC,UAAU;YAAChC,KAAK,EAAE;cAAElF,KAAK,EAAE;YAAO,CAAE;YAAAiF,QAAA,gBACnDlI,OAAA;cAAAkI,QAAA,gBACElI,OAAA,CAACK,IAAI;gBAAC+J,MAAM;gBAAAlC,QAAA,EAAC;cAAI;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxBzC,OAAA,CAACvB,MAAM;gBACL4L,IAAI,EAAC,UAAU;gBACflI,KAAK,EAAEb,aAAc;gBACrB2H,QAAQ,EAAE1H,gBAAiB;gBAC3B4G,KAAK,EAAE;kBAAElF,KAAK,EAAE,MAAM;kBAAEoG,SAAS,EAAE;gBAAE,CAAE;gBACvCiB,WAAW,EAAC,sCAAQ;gBAAApC,QAAA,EAEnBhG,gBAAgB,CAACqI,GAAG,CAAC1H,IAAI,iBACxB7C,OAAA,CAACM,MAAM;kBAAkB6B,KAAK,EAAEU,IAAI,CAACV,KAAM;kBAAA+F,QAAA,eACzClI,OAAA,CAAChC,KAAK;oBAAAkK,QAAA,GACHrF,IAAI,CAACR,IAAI,EACTQ,IAAI,CAACT,KAAK;kBAAA;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC,GAJGI,IAAI,CAACV,KAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENzC,OAAA;cAAAkI,QAAA,gBACElI,OAAA,CAACK,IAAI;gBAAC+J,MAAM;gBAAAlC,QAAA,EAAC;cAAK;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBzC,OAAA,CAACrB,MAAM;gBACLwD,KAAK,EAAEX,UAAW;gBAClByH,QAAQ,EAAExH,aAAc;gBACxB0G,KAAK,EAAE;kBAAEkB,SAAS,EAAE;gBAAE,CAAE;gBACxBmB,OAAO,EAAE;kBAAEC,SAAS,EAAGtI,KAAK,IAAK,GAAGA,KAAK;gBAAI;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACFzC,OAAA,CAACK,IAAI;gBAACwC,IAAI,EAAC,WAAW;gBAACsF,KAAK,EAAE;kBAAEiB,QAAQ,EAAE;gBAAG,CAAE;gBAAAlB,QAAA,GAAC,gBAC1C,EAAC1G,UAAU,EAAC,GAClB;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENzC,OAAA;cAAAkI,QAAA,eACElI,OAAA,CAAChC,KAAK;gBAACmK,KAAK,EAAE;kBAAElF,KAAK,EAAE,MAAM;kBAAE+G,cAAc,EAAE;gBAAgB,CAAE;gBAAA9B,QAAA,gBAC/DlI,OAAA,CAACK,IAAI;kBAAC+J,MAAM;kBAAAlC,QAAA,EAAC;gBAAI;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBzC,OAAA,CAACtB,MAAM;kBACLgM,OAAO,EAAEhJ,cAAe;kBACxBuH,QAAQ,EAAEtH;gBAAkB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENzC,OAAA;cAAAkI,QAAA,gBACElI,OAAA,CAACK,IAAI;gBAAC+J,MAAM;gBAAAlC,QAAA,EAAC;cAAK;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBzC,OAAA,CAACvB,MAAM;gBACL0D,KAAK,EAAEP,UAAW;gBAClBqH,QAAQ,EAAEpH,aAAc;gBACxBsG,KAAK,EAAE;kBAAElF,KAAK,EAAE,MAAM;kBAAEoG,SAAS,EAAE;gBAAE,CAAE;gBAAAnB,QAAA,gBAEvClI,OAAA,CAACM,MAAM;kBAAC6B,KAAK,EAAE,CAAE;kBAAA+F,QAAA,EAAC;gBAAE;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7BzC,OAAA,CAACM,MAAM;kBAAC6B,KAAK,EAAE,EAAG;kBAAA+F,QAAA,EAAC;gBAAG;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BzC,OAAA,CAACM,MAAM;kBAAC6B,KAAK,EAAE,EAAG;kBAAA+F,QAAA,EAAC;gBAAG;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BzC,OAAA,CAACM,MAAM;kBAAC6B,KAAK,EAAE,EAAG;kBAAA+F,QAAA,EAAC;gBAAG;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPzC,OAAA,CAAClC,IAAI;UAAC0K,KAAK,EAAC,0BAAM;UAAC9D,IAAI,EAAC,OAAO;UAACyD,KAAK,EAAE;YAAEkB,SAAS,EAAE;UAAG,CAAE;UAAAnB,QAAA,eACvDlI,OAAA,CAAC/B,GAAG;YAACqK,MAAM,EAAE,EAAG;YAAAJ,QAAA,gBACdlI,OAAA,CAAC9B,GAAG;cAACqK,IAAI,EAAE,EAAG;cAAAL,QAAA,eACZlI,OAAA,CAACjB,SAAS;gBACRyJ,KAAK,EAAC,oBAAK;gBACXrG,KAAK,EAAEvB,QAAQ,CAAC+J,MAAO;gBACvBC,MAAM,eAAE5K,OAAA,CAACN,eAAe;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BoI,MAAM,EAAC;cAAG;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzC,OAAA,CAAC9B,GAAG;cAACqK,IAAI,EAAE,EAAG;cAAAL,QAAA,eACZlI,OAAA,CAACjB,SAAS;gBACRyJ,KAAK,EAAC,0BAAM;gBACZrG,KAAK,EAAEvB,QAAQ,CAACkK,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACpG,OAAO,CAAC+F,MAAM,EAAE,CAAC,CAAE;gBAC9DC,MAAM,eAAE5K,OAAA,CAACP,YAAY;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBoI,MAAM,EAAC;cAAG;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA,CAAClC,IAAI;MACH0K,KAAK,eACHxI,OAAA,CAAChC,KAAK;QAAAkK,QAAA,gBACJlI,OAAA,CAACd,WAAW;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfzC,OAAA;UAAAkI,QAAA,EAAM;QAAI;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjBzC,OAAA,CAAChB,KAAK;UAACiM,KAAK,EAAErK,QAAQ,CAAC+J;QAAO;UAAArI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACR;MACDyI,KAAK,eACHlL,OAAA,CAAChC,KAAK;QAAAkK,QAAA,gBACJlI,OAAA,CAACjC,MAAM;UACLsE,IAAI,eAAErC,OAAA,CAACV,gBAAgB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3B4F,OAAO,EAAEA,CAAA,KAAMzJ,OAAO,CAACqI,IAAI,CAAC,SAAS,CAAE;UAAAiB,QAAA,EACxC;QAED;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzC,OAAA,CAACjC,MAAM;UACLsE,IAAI,eAAErC,OAAA,CAACR,aAAa;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB4F,OAAO,EAAET,QAAS;UAClBsB,QAAQ,EAAEtI,QAAQ,CAAC+J,MAAM,KAAK,CAAE;UAAAzC,QAAA,EACjC;QAED;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MACD0F,KAAK,EAAE;QAAEkB,SAAS,EAAE;MAAG,CAAE;MAAAnB,QAAA,EAExBtH,QAAQ,CAAC+J,MAAM,KAAK,CAAC,gBACpB3K,OAAA;QAAKmI,KAAK,EAAE;UAAEmB,SAAS,EAAE,QAAQ;UAAE6B,OAAO,EAAE,QAAQ;UAAEzI,KAAK,EAAE;QAAO,CAAE;QAAAwF,QAAA,gBACpElI,OAAA,CAACd,WAAW;UAACiJ,KAAK,EAAE;YAAEiB,QAAQ,EAAE,EAAE;YAAEhB,YAAY,EAAE;UAAG;QAAE;UAAA9F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DzC,OAAA;UAAAkI,QAAA,EAAK;QAAM;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjBzC,OAAA;UAAKmI,KAAK,EAAE;YAAEiB,QAAQ,EAAE;UAAG,CAAE;UAAAlB,QAAA,EAAC;QAAiB;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,gBAENzC,OAAA,CAAC/B,GAAG;QAACqK,MAAM,EAAE,EAAG;QAAAJ,QAAA,gBACdlI,OAAA,CAAC9B,GAAG;UAACqK,IAAI,EAAE,CAAE;UAAAL,QAAA,eACXlI,OAAA,CAAC3B,IAAI;YACHqG,IAAI,EAAC,OAAO;YACZ0G,UAAU,EAAExK,QAAS;YACrByK,UAAU,EAAGC,IAAI,iBACftL,OAAA,CAAC3B,IAAI,CAACkN,IAAI;cACRpD,KAAK,EAAE;gBACLqD,MAAM,EAAE,SAAS;gBACjBC,eAAe,EAAE,CAAA3K,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8B,EAAE,MAAK0I,IAAI,CAAC1I,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC1EuI,OAAO,EAAE,UAAU;gBACnBtB,YAAY,EAAE;cAChB,CAAE;cACFxB,OAAO,EAAEA,CAAA,KAAMtH,gBAAgB,CAACuK,IAAI,CAAE;cAAApD,QAAA,eAEtClI,OAAA,CAAC3B,IAAI,CAACkN,IAAI,CAACG,IAAI;gBACblD,KAAK,eACHxI,OAAA,CAAChC,KAAK;kBAAAkK,QAAA,gBACJlI,OAAA,CAACK,IAAI;oBAAC+J,MAAM;oBAACjC,KAAK,EAAE;sBAAEiB,QAAQ,EAAE;oBAAG,CAAE;oBAAAlB,QAAA,EAClCoD,IAAI,CAAC/G;kBAAQ;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACPzC,OAAA,CAAChB,KAAK;oBAACiM,KAAK,EAAEK,IAAI,CAAC1G,OAAO,CAAC+F,MAAO;oBAACjG,IAAI,EAAC;kBAAO;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CACR;gBACDkJ,WAAW,eACT3L,OAAA;kBAAAkI,QAAA,gBACElI,OAAA,CAACK,IAAI;oBAACwC,IAAI,EAAC,WAAW;oBAACsF,KAAK,EAAE;sBAAEiB,QAAQ,EAAE;oBAAG,CAAE;oBAAAlB,QAAA,EAC5CoD,IAAI,CAACtG;kBAAS;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACPzC,OAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNzC,OAAA,CAACK,IAAI;oBAACwC,IAAI,EAAC,WAAW;oBAACsF,KAAK,EAAE;sBAAEiB,QAAQ,EAAE;oBAAG,CAAE;oBAAAlB,QAAA,GAAC,4BACxC,EAACoD,IAAI,CAACpG,cAAc,CAAC0G,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzC,OAAA,CAAC9B,GAAG;UAACqK,IAAI,EAAE,EAAG;UAAAL,QAAA,EACXpH,aAAa,iBACZd,OAAA;YAAAkI,QAAA,gBACElI,OAAA;cAAKmI,KAAK,EAAE;gBAAEC,YAAY,EAAE,EAAE;gBAAEkB,SAAS,EAAE;cAAS,CAAE;cAAApB,QAAA,eACpDlI,OAAA,CAAC5B,KAAK;gBACJyN,GAAG,EAAE/K,aAAa,CAAC2D,GAAI;gBACvBqH,GAAG,EAAEhL,aAAa,CAACyD,QAAS;gBAC5B4D,KAAK,EAAE;kBAAEwB,QAAQ,EAAE,MAAM;kBAAEoC,SAAS,EAAE;gBAAI,CAAE;gBAC5CtG,OAAO,EAAE;kBACPuG,IAAI,eACFhM,OAAA,CAAChC,KAAK;oBAAAkK,QAAA,gBACJlI,OAAA,CAACT,cAAc;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEpB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAEX;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzC,OAAA,CAAClB,OAAO;cAACmN,WAAW,EAAC,MAAM;cAAA/D,QAAA,EAAC;YAAI;cAAA5F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAE1CzC,OAAA,CAAC3B,IAAI;cACH+M,UAAU,EAAEtK,aAAa,CAAC8D,OAAQ;cAClCyG,UAAU,EAAGpF,MAAM;gBAAA,IAAAiG,qBAAA;gBAAA,oBACjBlM,OAAA,CAAC3B,IAAI,CAACkN,IAAI;kBAAArD,QAAA,eACRlI,OAAA,CAAC3B,IAAI,CAACkN,IAAI,CAACG,IAAI;oBACbS,MAAM,eACJnM,OAAA;sBAAKmI,KAAK,EAAE;wBACVlF,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,EAAE;wBACV2G,YAAY,EAAE,KAAK;wBACnB4B,eAAe,EAAExD,YAAY,CAAChC,MAAM,CAACpD,IAAI,CAAC;wBAC1CiH,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,QAAQ;wBACxBtH,KAAK,EAAE;sBACT,CAAE;sBAAAwF,QAAA,EACCL,WAAW,CAAC5B,MAAM,CAACpD,IAAI;oBAAC;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CACN;oBACD+F,KAAK,eACHxI,OAAA,CAAChC,KAAK;sBAAAkK,QAAA,gBACJlI,OAAA,CAACK,IAAI;wBAAC+J,MAAM;wBAAAlC,QAAA,EAAEjC,MAAM,CAAC7D;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClCzC,OAAA,CAAC1B,GAAG;wBAACoE,KAAK,EAAEuF,YAAY,CAAChC,MAAM,CAACpD,IAAI,CAAE;wBAAAqF,QAAA,GAAAgE,qBAAA,GACnChK,gBAAgB,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7F,KAAK,KAAK8D,MAAM,CAACpD,IAAI,CAAC,cAAAqJ,qBAAA,uBAAnDA,qBAAA,CAAqD9J;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD,CAAC,eACNzC,OAAA,CAAC1B,GAAG;wBAACoE,KAAK,EAAEuD,MAAM,CAACzE,UAAU,IAAI,GAAG,GAAG,OAAO,GAAGyE,MAAM,CAACzE,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAM;wBAAA0G,QAAA,GAC1F,CAACjC,MAAM,CAACzE,UAAU,GAAG,GAAG,EAAEoK,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;sBAAA;wBAAAtJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACR;oBACDkJ,WAAW,eACT3L,OAAA;sBAAAkI,QAAA,GACGjC,MAAM,CAACnD,IAAI,iBACV9C,OAAA,CAACK,IAAI;wBAACwC,IAAI,EAAC,WAAW;wBAACsF,KAAK,EAAE;0BAAEiB,QAAQ,EAAE;wBAAG,CAAE;wBAAAlB,QAAA,GAAC,iBACzC,EAACjC,MAAM,CAACnD,IAAI,CAACC,CAAC,EAAC,IAAE,EAACkD,MAAM,CAACnD,IAAI,CAACE,CAAC,EAAC,kBACjC,EAACiD,MAAM,CAACnD,IAAI,CAACG,KAAK,EAAC,MAAC,EAACgD,MAAM,CAACnD,IAAI,CAACI,MAAM;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CACP,EACAwD,MAAM,CAAC9C,UAAU,iBAChBnD,OAAA;wBAAKmI,KAAK,EAAE;0BAAEkB,SAAS,EAAE;wBAAE,CAAE;wBAAAnB,QAAA,EAC1BkE,MAAM,CAACC,OAAO,CAACpG,MAAM,CAAC9C,UAAU,CAAC,CAACoH,GAAG,CAAC,CAAC,CAAC+B,GAAG,EAAEnK,KAAK,CAAC,kBAClDnC,OAAA,CAAC1B,GAAG;0BAAA4J,QAAA,GACDoE,GAAG,EAAC,IAAE,EAACnK,KAAK;wBAAA,GADLmK,GAAG;0BAAAhK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAER,CACN;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;YACZ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPzC,OAAA,CAACnB,KAAK;MACJ0N,IAAI,EAAErL,cAAe;MACrBsH,KAAK,EAAC,0BAAM;MACZgE,MAAM,EAAE,IAAK;MACbC,QAAQ,EAAEA,CAAA,KAAMtL,iBAAiB,CAAC,KAAK,CAAE;MACzC8B,KAAK,EAAE,GAAI;MAAAiF,QAAA,eAEXlI,OAAA;QAAK8L,GAAG,EAAC,SAAS;QAAC3D,KAAK,EAAE;UAAElF,KAAK,EAAE;QAAO,CAAE;QAAC4I,GAAG,EAAEzK;MAAa;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjC,EAAA,CAtiBID,iBAA2B;EAAA,QACdT,WAAW;AAAA;AAAA4M,EAAA,GADxBnM,iBAA2B;AAwiBjC,eAAeA,iBAAiB;AAAC,IAAAmM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}