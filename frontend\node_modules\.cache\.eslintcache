[{"D:\\customerDemo\\Link-Agent\\frontend\\src\\index.tsx": "1", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Agent\\AgentList.tsx": "2", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Agent\\AgentCreate.tsx": "3", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Dashboard\\index.tsx": "4", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\SpeechRecognition.tsx": "5", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\VisionRecognition.tsx": "6", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\DocumentRecognition.tsx": "7", "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\BiometricRecognition.tsx": "8"}, {"size": 1252, "mtime": 1756298156798, "results": "9", "hashOfConfig": "10"}, {"size": 12474, "mtime": 1756338706904, "results": "11", "hashOfConfig": "10"}, {"size": 12788, "mtime": 1756339751565, "results": "12", "hashOfConfig": "10"}, {"size": 16335, "mtime": 1756338584141, "results": "13", "hashOfConfig": "10"}, {"size": 15575, "mtime": 1756339884980, "results": "14", "hashOfConfig": "10"}, {"size": 20600, "mtime": 1756340033495, "results": "15", "hashOfConfig": "10"}, {"size": 25838, "mtime": 1756340192221, "results": "16", "hashOfConfig": "10"}, {"size": 29421, "mtime": 1756340357582, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10cbgwj", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\customerDemo\\Link-Agent\\frontend\\src\\index.tsx", [], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Agent\\AgentList.tsx", ["42", "43", "44", "45", "46", "47", "48", "49", "50"], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Agent\\AgentCreate.tsx", ["51", "52", "53", "54", "55"], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Dashboard\\index.tsx", ["56", "57", "58", "59", "60"], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\SpeechRecognition.tsx", ["61", "62", "63", "64", "65", "66"], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\VisionRecognition.tsx", ["67", "68", "69", "70", "71", "72", "73", "74", "75"], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\DocumentRecognition.tsx", ["76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87"], [], "D:\\customerDemo\\Link-Agent\\frontend\\src\\pages\\Recognition\\BiometricRecognition.tsx", ["88", "89", "90", "91", "92"], [], {"ruleId": "93", "severity": 1, "message": "94", "line": 21, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 21, "endColumn": 17}, {"ruleId": "93", "severity": 1, "message": "97", "line": 30, "column": 26, "nodeType": "95", "messageId": "96", "endLine": 30, "endColumn": 40}, {"ruleId": "93", "severity": 1, "message": "98", "line": 31, "column": 10, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 21}, {"ruleId": "93", "severity": 1, "message": "99", "line": 31, "column": 23, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 34}, {"ruleId": "93", "severity": 1, "message": "100", "line": 31, "column": 36, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 46}, {"ruleId": "93", "severity": 1, "message": "101", "line": 31, "column": 48, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 61}, {"ruleId": "93", "severity": 1, "message": "102", "line": 61, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 61, "endColumn": 17}, {"ruleId": "93", "severity": 1, "message": "103", "line": 67, "column": 10, "nodeType": "95", "messageId": "96", "endLine": 67, "endColumn": 17}, {"ruleId": "104", "severity": 1, "message": "105", "line": 144, "column": 6, "nodeType": "106", "endLine": 144, "endColumn": 8, "suggestions": "107"}, {"ruleId": "93", "severity": 1, "message": "108", "line": 17, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 17, "endColumn": 6}, {"ruleId": "93", "severity": 1, "message": "109", "line": 24, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 24, "endColumn": 21}, {"ruleId": "93", "severity": 1, "message": "110", "line": 25, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 25, "endColumn": 22}, {"ruleId": "93", "severity": 1, "message": "111", "line": 26, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 26, "endColumn": 28}, {"ruleId": "93", "severity": 1, "message": "112", "line": 31, "column": 27, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 31}, {"ruleId": "93", "severity": 1, "message": "113", "line": 1, "column": 17, "nodeType": "95", "messageId": "96", "endLine": 1, "endColumn": 26}, {"ruleId": "93", "severity": 1, "message": "114", "line": 9, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 9, "endColumn": 20}, {"ruleId": "93", "severity": 1, "message": "115", "line": 16, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 16, "endColumn": 18}, {"ruleId": "93", "severity": 1, "message": "116", "line": 23, "column": 10, "nodeType": "95", "messageId": "96", "endLine": 23, "endColumn": 22}, {"ruleId": "93", "severity": 1, "message": "102", "line": 31, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 17}, {"ruleId": "93", "severity": 1, "message": "117", "line": 19, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 19, "endColumn": 7}, {"ruleId": "93", "severity": 1, "message": "118", "line": 38, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 38, "endColumn": 14}, {"ruleId": "93", "severity": 1, "message": "119", "line": 38, "column": 16, "nodeType": "95", "messageId": "96", "endLine": 38, "endColumn": 25}, {"ruleId": "93", "severity": 1, "message": "120", "line": 66, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 66, "endColumn": 24}, {"ruleId": "93", "severity": 1, "message": "121", "line": 67, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 67, "endColumn": 20}, {"ruleId": "93", "severity": 1, "message": "122", "line": 68, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 68, "endColumn": 17}, {"ruleId": "93", "severity": 1, "message": "123", "line": 1, "column": 35, "nodeType": "95", "messageId": "96", "endLine": 1, "endColumn": 46}, {"ruleId": "93", "severity": 1, "message": "124", "line": 14, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 14, "endColumn": 8}, {"ruleId": "93", "severity": 1, "message": "125", "line": 24, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 24, "endColumn": 10}, {"ruleId": "93", "severity": 1, "message": "126", "line": 34, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 34, "endColumn": 18}, {"ruleId": "93", "severity": 1, "message": "127", "line": 35, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 35, "endColumn": 21}, {"ruleId": "93", "severity": 1, "message": "128", "line": 36, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 36, "endColumn": 22}, {"ruleId": "93", "severity": 1, "message": "129", "line": 43, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 43, "endColumn": 19}, {"ruleId": "93", "severity": 1, "message": "118", "line": 48, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 48, "endColumn": 14}, {"ruleId": "93", "severity": 1, "message": "119", "line": 48, "column": 16, "nodeType": "95", "messageId": "96", "endLine": 48, "endColumn": 25}, {"ruleId": "93", "severity": 1, "message": "124", "line": 14, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 14, "endColumn": 8}, {"ruleId": "93", "severity": 1, "message": "130", "line": 21, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 21, "endColumn": 10}, {"ruleId": "93", "severity": 1, "message": "131", "line": 38, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 38, "endColumn": 18}, {"ruleId": "93", "severity": 1, "message": "132", "line": 39, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 39, "endColumn": 16}, {"ruleId": "93", "severity": 1, "message": "133", "line": 45, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 45, "endColumn": 19}, {"ruleId": "93", "severity": 1, "message": "134", "line": 46, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 46, "endColumn": 20}, {"ruleId": "93", "severity": 1, "message": "94", "line": 47, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 47, "endColumn": 17}, {"ruleId": "93", "severity": 1, "message": "118", "line": 54, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 54, "endColumn": 14}, {"ruleId": "93", "severity": 1, "message": "119", "line": 54, "column": 16, "nodeType": "95", "messageId": "96", "endLine": 54, "endColumn": 25}, {"ruleId": "93", "severity": 1, "message": "135", "line": 56, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 56, "endColumn": 17}, {"ruleId": "93", "severity": 1, "message": "136", "line": 101, "column": 10, "nodeType": "95", "messageId": "96", "endLine": 101, "endColumn": 23}, {"ruleId": "93", "severity": 1, "message": "137", "line": 101, "column": 25, "nodeType": "95", "messageId": "96", "endLine": 101, "endColumn": 41}, {"ruleId": "93", "severity": 1, "message": "113", "line": 1, "column": 35, "nodeType": "95", "messageId": "96", "endLine": 1, "endColumn": 44}, {"ruleId": "93", "severity": 1, "message": "138", "line": 25, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 25, "endColumn": 9}, {"ruleId": "93", "severity": 1, "message": "139", "line": 26, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 26, "endColumn": 7}, {"ruleId": "93", "severity": 1, "message": "118", "line": 51, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 51, "endColumn": 14}, {"ruleId": "93", "severity": 1, "message": "119", "line": 51, "column": 16, "nodeType": "95", "messageId": "96", "endLine": 51, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'SearchOutlined' is defined but never used.", "Identifier", "unusedVar", "'useAppSelector' is defined but never used.", "'fetchAgents' is defined but never used.", "'deleteAgent' is defined but never used.", "'setFilters' is defined but never used.", "'setPagination' is defined but never used.", "'dispatch' is assigned a value but never used.", "'filters' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockAgents'. Either include it or remove the dependency array.", "ArrayExpression", ["140"], "'Tag' is defined but never used.", "'InfoCircleOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'Text' is assigned a value but never used.", "'useEffect' is defined but never used.", "'ArrowDownOutlined' is defined but never used.", "'WarningOutlined' is defined but never used.", "'formatNumber' is defined but never used.", "'Tabs' is defined but never used.", "'Title' is assigned a value but never used.", "'Paragraph' is assigned a value but never used.", "'audioContextRef' is assigned a value but never used.", "'analyserRef' is assigned a value but never used.", "'timerRef' is assigned a value but never used.", "'useCallback' is defined but never used.", "'Alert' is defined but never used.", "'Tooltip' is defined but never used.", "'ZoomOutOutlined' is defined but never used.", "'RotateLeftOutlined' is defined but never used.", "'RotateRightOutlined' is defined but never used.", "'ShoppingOutlined' is defined but never used.", "'Divider' is defined but never used.", "'PictureOutlined' is defined but never used.", "'TableOutlined' is defined but never used.", "'FileWordOutlined' is defined but never used.", "'FileExcelOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "'selectedTypes' is assigned a value but never used.", "'setSelectedTypes' is assigned a value but never used.", "'Result' is defined but never used.", "'Spin' is defined but never used.", {"desc": "141", "fix": "142"}, "Update the dependencies array to be: [mockAgents]", {"range": "143", "text": "144"}, [3455, 3457], "[mockAgents]"]