{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-Agent\\\\frontend\\\\src\\\\pages\\\\Agent\\\\AgentCreate.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Typography, Card, Button, Space, Steps, Form, Input, Select } from 'antd';\nimport { ArrowLeftOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { ROUTES } from '../../utils/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Step\n} = Steps;\n\n// 智能体类型定义\n\nconst AgentCreate = () => {\n  _s();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(ROUTES.AGENTS),\n          children: \"\\u8FD4\\u56DE\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n          style: {\n            fontSize: 64,\n            color: '#1890ff',\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u521B\\u5EFA\\u667A\\u80FD\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          type: \"secondary\",\n          children: \"\\u667A\\u80FD\\u4F53\\u521B\\u5EFA\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: 24\n          },\n          children: \"\\u8FD9\\u91CC\\u5C06\\u63D0\\u4F9B\\u5B8C\\u6574\\u7684\\u667A\\u80FD\\u4F53\\u521B\\u5EFA\\u5411\\u5BFC\\uFF0C\\u5305\\u62EC\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            textAlign: 'left',\n            display: 'inline-block',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u57FA\\u672C\\u4FE1\\u606F\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u6A21\\u578B\\u9009\\u62E9\\u548C\\u53C2\\u6570\\u8C03\\u4F18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u80FD\\u529B\\u548C\\u6280\\u80FD\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u9AD8\\u7EA7\\u8BBE\\u7F6E\\u548C\\u90E8\\u7F72\\u9009\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentCreate, \"GXMhA5qQK1BSRJuUyTrPnrEXMhI=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = AgentCreate;\nexport default AgentCreate;\nvar _c;\n$RefreshReg$(_c, \"AgentCreate\");", "map": {"version": 3, "names": ["React", "useState", "Typography", "Card", "<PERSON><PERSON>", "Space", "Steps", "Form", "Input", "Select", "ArrowLeftOutlined", "RobotOutlined", "useNavigate", "ROUTES", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Text", "TextArea", "Option", "Step", "AgentCreate", "_s", "navigate", "form", "useForm", "currentStep", "setCurrentStep", "loading", "setLoading", "formData", "setFormData", "validationErrors", "setValidationErrors", "children", "style", "marginBottom", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "AGENTS", "textAlign", "padding", "fontSize", "color", "level", "type", "display", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-Agent/frontend/src/pages/Agent/AgentCreate.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Typo<PERSON>,\n  Card,\n  Button,\n  Space,\n  Steps,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Switch,\n  Row,\n  Col,\n  Divider,\n  message,\n  Tag,\n  Tooltip,\n  Alert,\n  Upload,\n  Progress,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  RobotOutlined,\n  SaveOutlined,\n  InfoCircleOutlined,\n  UploadOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { ROUTES } from '../../utils/constants';\n\nconst { Title, Paragraph, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Step } = Steps;\n\n// 智能体类型定义\ninterface AgentFormData {\n  // 基本信息\n  name: string;\n  description: string;\n  type: 'chatbot' | 'workflow' | 'recognition' | 'analysis' | 'contract' | 'document';\n  category: string;\n  tags: string[];\n\n  // 模型配置\n  model: string;\n  modelProvider: 'openai' | 'anthropic' | 'google' | 'local' | 'azure';\n\n  // 能力配置\n  capabilities: string[];\n  knowledgeBase: string[];\n\n  // 高级配置\n  config: {\n    maxTokens: number;\n    temperature: number;\n    topP: number;\n    frequencyPenalty: number;\n    presencePenalty: number;\n    timeout: number;\n    retryCount: number;\n    systemPrompt: string;\n    enableMemory: boolean;\n    memorySize: number;\n    enableLogging: boolean;\n    enableAnalytics: boolean;\n  };\n\n  // 部署配置\n  deployment: {\n    environment: 'development' | 'staging' | 'production';\n    autoScale: boolean;\n    minInstances: number;\n    maxInstances: number;\n    resourceLimits: {\n      cpu: string;\n      memory: string;\n    };\n  };\n}\n\nconst AgentCreate: React.FC = () => {\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState<Partial<AgentFormData>>({});\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(ROUTES.AGENTS)}\n          >\n            返回列表\n          </Button>\n        </Space>\n      </div>\n\n      <Card>\n        <div style={{ textAlign: 'center', padding: '40px 0' }}>\n          <RobotOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />\n          <Title level={2}>\n            创建智能体\n          </Title>\n          <Paragraph type=\"secondary\">\n            智能体创建功能正在开发中，敬请期待...\n          </Paragraph>\n          <p style={{ color: '#666', marginBottom: 24 }}>\n            这里将提供完整的智能体创建向导，包括：\n          </p>\n          <ul style={{ textAlign: 'left', display: 'inline-block', color: '#666' }}>\n            <li>基本信息配置</li>\n            <li>模型选择和参数调优</li>\n            <li>能力和技能配置</li>\n            <li>高级设置和部署选项</li>\n          </ul>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AgentCreate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,QAYD,MAAM;AACb,SACEC,iBAAiB,EACjBC,aAAa,QAMR,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAGhB,UAAU;AAC7C,MAAM;EAAEiB;AAAS,CAAC,GAAGX,KAAK;AAC1B,MAAM;EAAEY;AAAO,CAAC,GAAGX,MAAM;AACzB,MAAM;EAAEY;AAAK,CAAC,GAAGf,KAAK;;AAEtB;;AA8CA,MAAMgB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,IAAI,CAAC,GAAGlB,IAAI,CAACmB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAyB,CAAC,CAAC,CAAC;EACpE,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAEpF,oBACEc,OAAA;IAAAoB,QAAA,gBACEpB,OAAA;MAAKqB,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAF,QAAA,eAC/BpB,OAAA,CAACV,KAAK;QAAA8B,QAAA,eACJpB,OAAA,CAACX,MAAM;UACLkC,IAAI,eAAEvB,OAAA,CAACL,iBAAiB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BC,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAACX,MAAM,CAAC+B,MAAM,CAAE;UAAAT,QAAA,EACxC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN3B,OAAA,CAACZ,IAAI;MAAAgC,QAAA,eACHpB,OAAA;QAAKqB,KAAK,EAAE;UAAES,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAX,QAAA,gBACrDpB,OAAA,CAACJ,aAAa;UAACyB,KAAK,EAAE;YAAEW,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEX,YAAY,EAAE;UAAG;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9E3B,OAAA,CAACC,KAAK;UAACiC,KAAK,EAAE,CAAE;UAAAd,QAAA,EAAC;QAEjB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3B,OAAA,CAACE,SAAS;UAACiC,IAAI,EAAC,WAAW;UAAAf,QAAA,EAAC;QAE5B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZ3B,OAAA;UAAGqB,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEX,YAAY,EAAE;UAAG,CAAE;UAAAF,QAAA,EAAC;QAE/C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ3B,OAAA;UAAIqB,KAAK,EAAE;YAAES,SAAS,EAAE,MAAM;YAAEM,OAAO,EAAE,cAAc;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACvEpB,OAAA;YAAAoB,QAAA,EAAI;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf3B,OAAA;YAAAoB,QAAA,EAAI;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClB3B,OAAA;YAAAoB,QAAA,EAAI;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB3B,OAAA;YAAAoB,QAAA,EAAI;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnB,EAAA,CA3CID,WAAqB;EAAA,QACRV,WAAW,EACbL,IAAI,CAACmB,OAAO;AAAA;AAAA0B,EAAA,GAFvB9B,WAAqB;AA6C3B,eAAeA,WAAW;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}