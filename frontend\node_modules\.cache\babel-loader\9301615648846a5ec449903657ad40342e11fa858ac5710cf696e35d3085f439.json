{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// id may be function name of Object, add a prefix to avoid this problem.\nfunction generateNodeKey(id) {\n  return '_EC_' + id;\n}\nvar Graph = /** @class */function () {\n  function Graph(directed) {\n    this.type = 'graph';\n    this.nodes = [];\n    this.edges = [];\n    this._nodesMap = {};\n    /**\r\n     * @type {Object.<string, module:echarts/data/Graph.Edge>}\r\n     * @private\r\n     */\n    this._edgesMap = {};\n    this._directed = directed || false;\n  }\n  /**\r\n   * If is directed graph\r\n   */\n  Graph.prototype.isDirected = function () {\n    return this._directed;\n  };\n  ;\n  /**\r\n   * Add a new node\r\n   */\n  Graph.prototype.addNode = function (id, dataIndex) {\n    id = id == null ? '' + dataIndex : '' + id;\n    var nodesMap = this._nodesMap;\n    if (nodesMap[generateNodeKey(id)]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Graph nodes have duplicate name or id');\n      }\n      return;\n    }\n    var node = new GraphNode(id, dataIndex);\n    node.hostGraph = this;\n    this.nodes.push(node);\n    nodesMap[generateNodeKey(id)] = node;\n    return node;\n  };\n  ;\n  /**\r\n   * Get node by data index\r\n   */\n  Graph.prototype.getNodeByIndex = function (dataIndex) {\n    var rawIdx = this.data.getRawIndex(dataIndex);\n    return this.nodes[rawIdx];\n  };\n  ;\n  /**\r\n   * Get node by id\r\n   */\n  Graph.prototype.getNodeById = function (id) {\n    return this._nodesMap[generateNodeKey(id)];\n  };\n  ;\n  /**\r\n   * Add a new edge\r\n   */\n  Graph.prototype.addEdge = function (n1, n2, dataIndex) {\n    var nodesMap = this._nodesMap;\n    var edgesMap = this._edgesMap;\n    // PENDING\n    if (zrUtil.isNumber(n1)) {\n      n1 = this.nodes[n1];\n    }\n    if (zrUtil.isNumber(n2)) {\n      n2 = this.nodes[n2];\n    }\n    if (!(n1 instanceof GraphNode)) {\n      n1 = nodesMap[generateNodeKey(n1)];\n    }\n    if (!(n2 instanceof GraphNode)) {\n      n2 = nodesMap[generateNodeKey(n2)];\n    }\n    if (!n1 || !n2) {\n      return;\n    }\n    var key = n1.id + '-' + n2.id;\n    var edge = new GraphEdge(n1, n2, dataIndex);\n    edge.hostGraph = this;\n    if (this._directed) {\n      n1.outEdges.push(edge);\n      n2.inEdges.push(edge);\n    }\n    n1.edges.push(edge);\n    if (n1 !== n2) {\n      n2.edges.push(edge);\n    }\n    this.edges.push(edge);\n    edgesMap[key] = edge;\n    return edge;\n  };\n  ;\n  /**\r\n   * Get edge by data index\r\n   */\n  Graph.prototype.getEdgeByIndex = function (dataIndex) {\n    var rawIdx = this.edgeData.getRawIndex(dataIndex);\n    return this.edges[rawIdx];\n  };\n  ;\n  /**\r\n   * Get edge by two linked nodes\r\n   */\n  Graph.prototype.getEdge = function (n1, n2) {\n    if (n1 instanceof GraphNode) {\n      n1 = n1.id;\n    }\n    if (n2 instanceof GraphNode) {\n      n2 = n2.id;\n    }\n    var edgesMap = this._edgesMap;\n    if (this._directed) {\n      return edgesMap[n1 + '-' + n2];\n    } else {\n      return edgesMap[n1 + '-' + n2] || edgesMap[n2 + '-' + n1];\n    }\n  };\n  ;\n  /**\r\n   * Iterate all nodes\r\n   */\n  Graph.prototype.eachNode = function (cb, context) {\n    var nodes = this.nodes;\n    var len = nodes.length;\n    for (var i = 0; i < len; i++) {\n      if (nodes[i].dataIndex >= 0) {\n        cb.call(context, nodes[i], i);\n      }\n    }\n  };\n  ;\n  /**\r\n   * Iterate all edges\r\n   */\n  Graph.prototype.eachEdge = function (cb, context) {\n    var edges = this.edges;\n    var len = edges.length;\n    for (var i = 0; i < len; i++) {\n      if (edges[i].dataIndex >= 0 && edges[i].node1.dataIndex >= 0 && edges[i].node2.dataIndex >= 0) {\n        cb.call(context, edges[i], i);\n      }\n    }\n  };\n  ;\n  /**\r\n   * Breadth first traverse\r\n   * Return true to stop traversing\r\n   */\n  Graph.prototype.breadthFirstTraverse = function (cb, startNode, direction, context) {\n    if (!(startNode instanceof GraphNode)) {\n      startNode = this._nodesMap[generateNodeKey(startNode)];\n    }\n    if (!startNode) {\n      return;\n    }\n    var edgeType = direction === 'out' ? 'outEdges' : direction === 'in' ? 'inEdges' : 'edges';\n    for (var i = 0; i < this.nodes.length; i++) {\n      this.nodes[i].__visited = false;\n    }\n    if (cb.call(context, startNode, null)) {\n      return;\n    }\n    var queue = [startNode];\n    while (queue.length) {\n      var currentNode = queue.shift();\n      var edges = currentNode[edgeType];\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        var otherNode = e.node1 === currentNode ? e.node2 : e.node1;\n        if (!otherNode.__visited) {\n          if (cb.call(context, otherNode, currentNode)) {\n            // Stop traversing\n            return;\n          }\n          queue.push(otherNode);\n          otherNode.__visited = true;\n        }\n      }\n    }\n  };\n  ;\n  // TODO\n  // depthFirstTraverse(\n  //     cb, startNode, direction, context\n  // ) {\n  // };\n  // Filter update\n  Graph.prototype.update = function () {\n    var data = this.data;\n    var edgeData = this.edgeData;\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n    edgeData.filterSelf(function (idx) {\n      var edge = edges[edgeData.getRawIndex(idx)];\n      return edge.node1.dataIndex >= 0 && edge.node2.dataIndex >= 0;\n    });\n    // Update edge\n    for (var i = 0, len = edges.length; i < len; i++) {\n      edges[i].dataIndex = -1;\n    }\n    for (var i = 0, len = edgeData.count(); i < len; i++) {\n      edges[edgeData.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  ;\n  /**\r\n   * @return {module:echarts/data/Graph}\r\n   */\n  Graph.prototype.clone = function () {\n    var graph = new Graph(this._directed);\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0; i < nodes.length; i++) {\n      graph.addNode(nodes[i].id, nodes[i].dataIndex);\n    }\n    for (var i = 0; i < edges.length; i++) {\n      var e = edges[i];\n      graph.addEdge(e.node1.id, e.node2.id, e.dataIndex);\n    }\n    return graph;\n  };\n  ;\n  return Graph;\n}();\nvar GraphNode = /** @class */function () {\n  function GraphNode(id, dataIndex) {\n    this.inEdges = [];\n    this.outEdges = [];\n    this.edges = [];\n    this.dataIndex = -1;\n    this.id = id == null ? '' : id;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  /**\r\n   * @return {number}\r\n   */\n  GraphNode.prototype.degree = function () {\n    return this.edges.length;\n  };\n  /**\r\n   * @return {number}\r\n   */\n  GraphNode.prototype.inDegree = function () {\n    return this.inEdges.length;\n  };\n  /**\r\n  * @return {number}\r\n  */\n  GraphNode.prototype.outDegree = function () {\n    return this.outEdges.length;\n  };\n  GraphNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphNode.prototype.getAdjacentDataIndices = function () {\n    var dataIndices = {\n      edge: [],\n      node: []\n    };\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      dataIndices.edge.push(adjacentEdge.dataIndex);\n      dataIndices.node.push(adjacentEdge.node1.dataIndex, adjacentEdge.node2.dataIndex);\n    }\n    return dataIndices;\n  };\n  GraphNode.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      connectedEdgesMap.set(adjacentEdge.dataIndex, true);\n      var sourceNodesQueue = [adjacentEdge.node1];\n      var targetNodesQueue = [adjacentEdge.node2];\n      var nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < sourceNodesQueue.length) {\n        var sourceNode = sourceNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(sourceNode.dataIndex, true);\n        for (var j = 0; j < sourceNode.inEdges.length; j++) {\n          connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n          sourceNodesQueue.push(sourceNode.inEdges[j].node1);\n        }\n      }\n      nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < targetNodesQueue.length) {\n        var targetNode = targetNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(targetNode.dataIndex, true);\n        for (var j = 0; j < targetNode.outEdges.length; j++) {\n          connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n          targetNodesQueue.push(targetNode.outEdges[j].node2);\n        }\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphNode;\n}();\nvar GraphEdge = /** @class */function () {\n  function GraphEdge(n1, n2, dataIndex) {\n    this.dataIndex = -1;\n    this.node1 = n1;\n    this.node2 = n2;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  GraphEdge.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.edgeData.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphEdge.prototype.getAdjacentDataIndices = function () {\n    return {\n      edge: [this.dataIndex],\n      node: [this.node1.dataIndex, this.node2.dataIndex]\n    };\n  };\n  GraphEdge.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    connectedEdgesMap.set(this.dataIndex, true);\n    var sourceNodes = [this.node1];\n    var targetNodes = [this.node2];\n    var nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < sourceNodes.length) {\n      var sourceNode = sourceNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(sourceNode.dataIndex, true);\n      for (var j = 0; j < sourceNode.inEdges.length; j++) {\n        connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n        sourceNodes.push(sourceNode.inEdges[j].node1);\n      }\n    }\n    nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < targetNodes.length) {\n      var targetNode = targetNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(targetNode.dataIndex, true);\n      for (var j = 0; j < targetNode.outEdges.length; j++) {\n        connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n        targetNodes.push(targetNode.outEdges[j].node2);\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphEdge;\n}();\nfunction createGraphDataProxyMixin(hostName, dataName) {\n  return {\n    /**\r\n     * @param Default 'value'. can be 'a', 'b', 'c', 'd', 'e'.\r\n     */\n    getValue: function (dimension) {\n      var data = this[hostName][dataName];\n      return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n    },\n    // TODO: TYPE stricter type.\n    setVisual: function (key, value) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemVisual(this.dataIndex, key, value);\n    },\n    getVisual: function (key) {\n      return this[hostName][dataName].getItemVisual(this.dataIndex, key);\n    },\n    setLayout: function (layout, merge) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemLayout(this.dataIndex, layout, merge);\n    },\n    getLayout: function () {\n      return this[hostName][dataName].getItemLayout(this.dataIndex);\n    },\n    getGraphicEl: function () {\n      return this[hostName][dataName].getItemGraphicEl(this.dataIndex);\n    },\n    getRawIndex: function () {\n      return this[hostName][dataName].getRawIndex(this.dataIndex);\n    }\n  };\n}\n;\n;\n;\nzrUtil.mixin(GraphNode, createGraphDataProxyMixin('hostGraph', 'data'));\nzrUtil.mixin(GraphEdge, createGraphDataProxyMixin('hostGraph', 'edgeData'));\nexport default Graph;\nexport { GraphNode, GraphEdge };", "map": {"version": 3, "names": ["zrUtil", "generateNodeKey", "id", "Graph", "directed", "type", "nodes", "edges", "_nodesMap", "_edgesMap", "_directed", "prototype", "isDirected", "addNode", "dataIndex", "nodesMap", "process", "env", "NODE_ENV", "console", "error", "node", "GraphNode", "hostGraph", "push", "getNodeByIndex", "rawIdx", "data", "getRawIndex", "getNodeById", "addEdge", "n1", "n2", "edgesMap", "isNumber", "key", "edge", "GraphEdge", "outEdges", "inEdges", "getEdgeByIndex", "edgeData", "getEdge", "eachNode", "cb", "context", "len", "length", "i", "call", "eachEdge", "node1", "node2", "breadthFirstTraverse", "startNode", "direction", "edgeType", "__visited", "queue", "currentNode", "shift", "e", "otherNode", "update", "count", "filterSelf", "idx", "clone", "graph", "degree", "inDegree", "outDegree", "getModel", "path", "itemModel", "getItemModel", "getAdjacentDataIndices", "dataIndices", "adjacentEdge", "getTrajectoryDataIndices", "connectedEdgesMap", "createHashMap", "connectedNodesMap", "set", "sourceNodesQueue", "targetNodesQueue", "nodeIteratorIndex", "sourceNode", "j", "targetNode", "keys", "sourceNodes", "targetNodes", "createGraphDataProxyMixin", "hostName", "dataName", "getValue", "dimension", "getStore", "get", "getDimensionIndex", "setVisual", "value", "setItemVisual", "getVisual", "getItemVisual", "setLayout", "layout", "merge", "setItemLayout", "getLayout", "getItemLayout", "getGraphicEl", "getItemGraphicEl", "mixin"], "sources": ["D:/customerDemo/Link-Agent/frontend/node_modules/echarts/lib/data/Graph.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// id may be function name of Object, add a prefix to avoid this problem.\nfunction generateNodeKey(id) {\n  return '_EC_' + id;\n}\nvar Graph = /** @class */function () {\n  function Graph(directed) {\n    this.type = 'graph';\n    this.nodes = [];\n    this.edges = [];\n    this._nodesMap = {};\n    /**\r\n     * @type {Object.<string, module:echarts/data/Graph.Edge>}\r\n     * @private\r\n     */\n    this._edgesMap = {};\n    this._directed = directed || false;\n  }\n  /**\r\n   * If is directed graph\r\n   */\n  Graph.prototype.isDirected = function () {\n    return this._directed;\n  };\n  ;\n  /**\r\n   * Add a new node\r\n   */\n  Graph.prototype.addNode = function (id, dataIndex) {\n    id = id == null ? '' + dataIndex : '' + id;\n    var nodesMap = this._nodesMap;\n    if (nodesMap[generateNodeKey(id)]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Graph nodes have duplicate name or id');\n      }\n      return;\n    }\n    var node = new GraphNode(id, dataIndex);\n    node.hostGraph = this;\n    this.nodes.push(node);\n    nodesMap[generateNodeKey(id)] = node;\n    return node;\n  };\n  ;\n  /**\r\n   * Get node by data index\r\n   */\n  Graph.prototype.getNodeByIndex = function (dataIndex) {\n    var rawIdx = this.data.getRawIndex(dataIndex);\n    return this.nodes[rawIdx];\n  };\n  ;\n  /**\r\n   * Get node by id\r\n   */\n  Graph.prototype.getNodeById = function (id) {\n    return this._nodesMap[generateNodeKey(id)];\n  };\n  ;\n  /**\r\n   * Add a new edge\r\n   */\n  Graph.prototype.addEdge = function (n1, n2, dataIndex) {\n    var nodesMap = this._nodesMap;\n    var edgesMap = this._edgesMap;\n    // PENDING\n    if (zrUtil.isNumber(n1)) {\n      n1 = this.nodes[n1];\n    }\n    if (zrUtil.isNumber(n2)) {\n      n2 = this.nodes[n2];\n    }\n    if (!(n1 instanceof GraphNode)) {\n      n1 = nodesMap[generateNodeKey(n1)];\n    }\n    if (!(n2 instanceof GraphNode)) {\n      n2 = nodesMap[generateNodeKey(n2)];\n    }\n    if (!n1 || !n2) {\n      return;\n    }\n    var key = n1.id + '-' + n2.id;\n    var edge = new GraphEdge(n1, n2, dataIndex);\n    edge.hostGraph = this;\n    if (this._directed) {\n      n1.outEdges.push(edge);\n      n2.inEdges.push(edge);\n    }\n    n1.edges.push(edge);\n    if (n1 !== n2) {\n      n2.edges.push(edge);\n    }\n    this.edges.push(edge);\n    edgesMap[key] = edge;\n    return edge;\n  };\n  ;\n  /**\r\n   * Get edge by data index\r\n   */\n  Graph.prototype.getEdgeByIndex = function (dataIndex) {\n    var rawIdx = this.edgeData.getRawIndex(dataIndex);\n    return this.edges[rawIdx];\n  };\n  ;\n  /**\r\n   * Get edge by two linked nodes\r\n   */\n  Graph.prototype.getEdge = function (n1, n2) {\n    if (n1 instanceof GraphNode) {\n      n1 = n1.id;\n    }\n    if (n2 instanceof GraphNode) {\n      n2 = n2.id;\n    }\n    var edgesMap = this._edgesMap;\n    if (this._directed) {\n      return edgesMap[n1 + '-' + n2];\n    } else {\n      return edgesMap[n1 + '-' + n2] || edgesMap[n2 + '-' + n1];\n    }\n  };\n  ;\n  /**\r\n   * Iterate all nodes\r\n   */\n  Graph.prototype.eachNode = function (cb, context) {\n    var nodes = this.nodes;\n    var len = nodes.length;\n    for (var i = 0; i < len; i++) {\n      if (nodes[i].dataIndex >= 0) {\n        cb.call(context, nodes[i], i);\n      }\n    }\n  };\n  ;\n  /**\r\n   * Iterate all edges\r\n   */\n  Graph.prototype.eachEdge = function (cb, context) {\n    var edges = this.edges;\n    var len = edges.length;\n    for (var i = 0; i < len; i++) {\n      if (edges[i].dataIndex >= 0 && edges[i].node1.dataIndex >= 0 && edges[i].node2.dataIndex >= 0) {\n        cb.call(context, edges[i], i);\n      }\n    }\n  };\n  ;\n  /**\r\n   * Breadth first traverse\r\n   * Return true to stop traversing\r\n   */\n  Graph.prototype.breadthFirstTraverse = function (cb, startNode, direction, context) {\n    if (!(startNode instanceof GraphNode)) {\n      startNode = this._nodesMap[generateNodeKey(startNode)];\n    }\n    if (!startNode) {\n      return;\n    }\n    var edgeType = direction === 'out' ? 'outEdges' : direction === 'in' ? 'inEdges' : 'edges';\n    for (var i = 0; i < this.nodes.length; i++) {\n      this.nodes[i].__visited = false;\n    }\n    if (cb.call(context, startNode, null)) {\n      return;\n    }\n    var queue = [startNode];\n    while (queue.length) {\n      var currentNode = queue.shift();\n      var edges = currentNode[edgeType];\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        var otherNode = e.node1 === currentNode ? e.node2 : e.node1;\n        if (!otherNode.__visited) {\n          if (cb.call(context, otherNode, currentNode)) {\n            // Stop traversing\n            return;\n          }\n          queue.push(otherNode);\n          otherNode.__visited = true;\n        }\n      }\n    }\n  };\n  ;\n  // TODO\n  // depthFirstTraverse(\n  //     cb, startNode, direction, context\n  // ) {\n  // };\n  // Filter update\n  Graph.prototype.update = function () {\n    var data = this.data;\n    var edgeData = this.edgeData;\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n    edgeData.filterSelf(function (idx) {\n      var edge = edges[edgeData.getRawIndex(idx)];\n      return edge.node1.dataIndex >= 0 && edge.node2.dataIndex >= 0;\n    });\n    // Update edge\n    for (var i = 0, len = edges.length; i < len; i++) {\n      edges[i].dataIndex = -1;\n    }\n    for (var i = 0, len = edgeData.count(); i < len; i++) {\n      edges[edgeData.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  ;\n  /**\r\n   * @return {module:echarts/data/Graph}\r\n   */\n  Graph.prototype.clone = function () {\n    var graph = new Graph(this._directed);\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0; i < nodes.length; i++) {\n      graph.addNode(nodes[i].id, nodes[i].dataIndex);\n    }\n    for (var i = 0; i < edges.length; i++) {\n      var e = edges[i];\n      graph.addEdge(e.node1.id, e.node2.id, e.dataIndex);\n    }\n    return graph;\n  };\n  ;\n  return Graph;\n}();\nvar GraphNode = /** @class */function () {\n  function GraphNode(id, dataIndex) {\n    this.inEdges = [];\n    this.outEdges = [];\n    this.edges = [];\n    this.dataIndex = -1;\n    this.id = id == null ? '' : id;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  /**\r\n   * @return {number}\r\n   */\n  GraphNode.prototype.degree = function () {\n    return this.edges.length;\n  };\n  /**\r\n   * @return {number}\r\n   */\n  GraphNode.prototype.inDegree = function () {\n    return this.inEdges.length;\n  };\n  /**\r\n  * @return {number}\r\n  */\n  GraphNode.prototype.outDegree = function () {\n    return this.outEdges.length;\n  };\n  GraphNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphNode.prototype.getAdjacentDataIndices = function () {\n    var dataIndices = {\n      edge: [],\n      node: []\n    };\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      dataIndices.edge.push(adjacentEdge.dataIndex);\n      dataIndices.node.push(adjacentEdge.node1.dataIndex, adjacentEdge.node2.dataIndex);\n    }\n    return dataIndices;\n  };\n  GraphNode.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      connectedEdgesMap.set(adjacentEdge.dataIndex, true);\n      var sourceNodesQueue = [adjacentEdge.node1];\n      var targetNodesQueue = [adjacentEdge.node2];\n      var nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < sourceNodesQueue.length) {\n        var sourceNode = sourceNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(sourceNode.dataIndex, true);\n        for (var j = 0; j < sourceNode.inEdges.length; j++) {\n          connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n          sourceNodesQueue.push(sourceNode.inEdges[j].node1);\n        }\n      }\n      nodeIteratorIndex = 0;\n      while (nodeIteratorIndex < targetNodesQueue.length) {\n        var targetNode = targetNodesQueue[nodeIteratorIndex];\n        nodeIteratorIndex++;\n        connectedNodesMap.set(targetNode.dataIndex, true);\n        for (var j = 0; j < targetNode.outEdges.length; j++) {\n          connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n          targetNodesQueue.push(targetNode.outEdges[j].node2);\n        }\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphNode;\n}();\nvar GraphEdge = /** @class */function () {\n  function GraphEdge(n1, n2, dataIndex) {\n    this.dataIndex = -1;\n    this.node1 = n1;\n    this.node2 = n2;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  GraphEdge.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.edgeData.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphEdge.prototype.getAdjacentDataIndices = function () {\n    return {\n      edge: [this.dataIndex],\n      node: [this.node1.dataIndex, this.node2.dataIndex]\n    };\n  };\n  GraphEdge.prototype.getTrajectoryDataIndices = function () {\n    var connectedEdgesMap = zrUtil.createHashMap();\n    var connectedNodesMap = zrUtil.createHashMap();\n    connectedEdgesMap.set(this.dataIndex, true);\n    var sourceNodes = [this.node1];\n    var targetNodes = [this.node2];\n    var nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < sourceNodes.length) {\n      var sourceNode = sourceNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(sourceNode.dataIndex, true);\n      for (var j = 0; j < sourceNode.inEdges.length; j++) {\n        connectedEdgesMap.set(sourceNode.inEdges[j].dataIndex, true);\n        sourceNodes.push(sourceNode.inEdges[j].node1);\n      }\n    }\n    nodeIteratorIndex = 0;\n    while (nodeIteratorIndex < targetNodes.length) {\n      var targetNode = targetNodes[nodeIteratorIndex];\n      nodeIteratorIndex++;\n      connectedNodesMap.set(targetNode.dataIndex, true);\n      for (var j = 0; j < targetNode.outEdges.length; j++) {\n        connectedEdgesMap.set(targetNode.outEdges[j].dataIndex, true);\n        targetNodes.push(targetNode.outEdges[j].node2);\n      }\n    }\n    return {\n      edge: connectedEdgesMap.keys(),\n      node: connectedNodesMap.keys()\n    };\n  };\n  return GraphEdge;\n}();\nfunction createGraphDataProxyMixin(hostName, dataName) {\n  return {\n    /**\r\n     * @param Default 'value'. can be 'a', 'b', 'c', 'd', 'e'.\r\n     */\n    getValue: function (dimension) {\n      var data = this[hostName][dataName];\n      return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n    },\n    // TODO: TYPE stricter type.\n    setVisual: function (key, value) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemVisual(this.dataIndex, key, value);\n    },\n    getVisual: function (key) {\n      return this[hostName][dataName].getItemVisual(this.dataIndex, key);\n    },\n    setLayout: function (layout, merge) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemLayout(this.dataIndex, layout, merge);\n    },\n    getLayout: function () {\n      return this[hostName][dataName].getItemLayout(this.dataIndex);\n    },\n    getGraphicEl: function () {\n      return this[hostName][dataName].getItemGraphicEl(this.dataIndex);\n    },\n    getRawIndex: function () {\n      return this[hostName][dataName].getRawIndex(this.dataIndex);\n    }\n  };\n}\n;\n;\n;\nzrUtil.mixin(GraphNode, createGraphDataProxyMixin('hostGraph', 'data'));\nzrUtil.mixin(GraphEdge, createGraphDataProxyMixin('hostGraph', 'edgeData'));\nexport default Graph;\nexport { GraphNode, GraphEdge };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD;AACA,SAASC,eAAeA,CAACC,EAAE,EAAE;EAC3B,OAAO,MAAM,GAAGA,EAAE;AACpB;AACA,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB;AACJ;AACA;AACA;IACI,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGN,QAAQ,IAAI,KAAK;EACpC;EACA;AACF;AACA;EACED,KAAK,CAACQ,SAAS,CAACC,UAAU,GAAG,YAAY;IACvC,OAAO,IAAI,CAACF,SAAS;EACvB,CAAC;EACD;EACA;AACF;AACA;EACEP,KAAK,CAACQ,SAAS,CAACE,OAAO,GAAG,UAAUX,EAAE,EAAEY,SAAS,EAAE;IACjDZ,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,EAAE,GAAGY,SAAS,GAAG,EAAE,GAAGZ,EAAE;IAC1C,IAAIa,QAAQ,GAAG,IAAI,CAACP,SAAS;IAC7B,IAAIO,QAAQ,CAACd,eAAe,CAACC,EAAE,CAAC,CAAC,EAAE;MACjC,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;MACxD;MACA;IACF;IACA,IAAIC,IAAI,GAAG,IAAIC,SAAS,CAACpB,EAAE,EAAEY,SAAS,CAAC;IACvCO,IAAI,CAACE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACjB,KAAK,CAACkB,IAAI,CAACH,IAAI,CAAC;IACrBN,QAAQ,CAACd,eAAe,CAACC,EAAE,CAAC,CAAC,GAAGmB,IAAI;IACpC,OAAOA,IAAI;EACb,CAAC;EACD;EACA;AACF;AACA;EACElB,KAAK,CAACQ,SAAS,CAACc,cAAc,GAAG,UAAUX,SAAS,EAAE;IACpD,IAAIY,MAAM,GAAG,IAAI,CAACC,IAAI,CAACC,WAAW,CAACd,SAAS,CAAC;IAC7C,OAAO,IAAI,CAACR,KAAK,CAACoB,MAAM,CAAC;EAC3B,CAAC;EACD;EACA;AACF;AACA;EACEvB,KAAK,CAACQ,SAAS,CAACkB,WAAW,GAAG,UAAU3B,EAAE,EAAE;IAC1C,OAAO,IAAI,CAACM,SAAS,CAACP,eAAe,CAACC,EAAE,CAAC,CAAC;EAC5C,CAAC;EACD;EACA;AACF;AACA;EACEC,KAAK,CAACQ,SAAS,CAACmB,OAAO,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAElB,SAAS,EAAE;IACrD,IAAIC,QAAQ,GAAG,IAAI,CAACP,SAAS;IAC7B,IAAIyB,QAAQ,GAAG,IAAI,CAACxB,SAAS;IAC7B;IACA,IAAIT,MAAM,CAACkC,QAAQ,CAACH,EAAE,CAAC,EAAE;MACvBA,EAAE,GAAG,IAAI,CAACzB,KAAK,CAACyB,EAAE,CAAC;IACrB;IACA,IAAI/B,MAAM,CAACkC,QAAQ,CAACF,EAAE,CAAC,EAAE;MACvBA,EAAE,GAAG,IAAI,CAAC1B,KAAK,CAAC0B,EAAE,CAAC;IACrB;IACA,IAAI,EAAED,EAAE,YAAYT,SAAS,CAAC,EAAE;MAC9BS,EAAE,GAAGhB,QAAQ,CAACd,eAAe,CAAC8B,EAAE,CAAC,CAAC;IACpC;IACA,IAAI,EAAEC,EAAE,YAAYV,SAAS,CAAC,EAAE;MAC9BU,EAAE,GAAGjB,QAAQ,CAACd,eAAe,CAAC+B,EAAE,CAAC,CAAC;IACpC;IACA,IAAI,CAACD,EAAE,IAAI,CAACC,EAAE,EAAE;MACd;IACF;IACA,IAAIG,GAAG,GAAGJ,EAAE,CAAC7B,EAAE,GAAG,GAAG,GAAG8B,EAAE,CAAC9B,EAAE;IAC7B,IAAIkC,IAAI,GAAG,IAAIC,SAAS,CAACN,EAAE,EAAEC,EAAE,EAAElB,SAAS,CAAC;IAC3CsB,IAAI,CAACb,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACb,SAAS,EAAE;MAClBqB,EAAE,CAACO,QAAQ,CAACd,IAAI,CAACY,IAAI,CAAC;MACtBJ,EAAE,CAACO,OAAO,CAACf,IAAI,CAACY,IAAI,CAAC;IACvB;IACAL,EAAE,CAACxB,KAAK,CAACiB,IAAI,CAACY,IAAI,CAAC;IACnB,IAAIL,EAAE,KAAKC,EAAE,EAAE;MACbA,EAAE,CAACzB,KAAK,CAACiB,IAAI,CAACY,IAAI,CAAC;IACrB;IACA,IAAI,CAAC7B,KAAK,CAACiB,IAAI,CAACY,IAAI,CAAC;IACrBH,QAAQ,CAACE,GAAG,CAAC,GAAGC,IAAI;IACpB,OAAOA,IAAI;EACb,CAAC;EACD;EACA;AACF;AACA;EACEjC,KAAK,CAACQ,SAAS,CAAC6B,cAAc,GAAG,UAAU1B,SAAS,EAAE;IACpD,IAAIY,MAAM,GAAG,IAAI,CAACe,QAAQ,CAACb,WAAW,CAACd,SAAS,CAAC;IACjD,OAAO,IAAI,CAACP,KAAK,CAACmB,MAAM,CAAC;EAC3B,CAAC;EACD;EACA;AACF;AACA;EACEvB,KAAK,CAACQ,SAAS,CAAC+B,OAAO,GAAG,UAAUX,EAAE,EAAEC,EAAE,EAAE;IAC1C,IAAID,EAAE,YAAYT,SAAS,EAAE;MAC3BS,EAAE,GAAGA,EAAE,CAAC7B,EAAE;IACZ;IACA,IAAI8B,EAAE,YAAYV,SAAS,EAAE;MAC3BU,EAAE,GAAGA,EAAE,CAAC9B,EAAE;IACZ;IACA,IAAI+B,QAAQ,GAAG,IAAI,CAACxB,SAAS;IAC7B,IAAI,IAAI,CAACC,SAAS,EAAE;MAClB,OAAOuB,QAAQ,CAACF,EAAE,GAAG,GAAG,GAAGC,EAAE,CAAC;IAChC,CAAC,MAAM;MACL,OAAOC,QAAQ,CAACF,EAAE,GAAG,GAAG,GAAGC,EAAE,CAAC,IAAIC,QAAQ,CAACD,EAAE,GAAG,GAAG,GAAGD,EAAE,CAAC;IAC3D;EACF,CAAC;EACD;EACA;AACF;AACA;EACE5B,KAAK,CAACQ,SAAS,CAACgC,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IAChD,IAAIvC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIwC,GAAG,GAAGxC,KAAK,CAACyC,MAAM;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5B,IAAI1C,KAAK,CAAC0C,CAAC,CAAC,CAAClC,SAAS,IAAI,CAAC,EAAE;QAC3B8B,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEvC,KAAK,CAAC0C,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EACD;EACA;AACF;AACA;EACE7C,KAAK,CAACQ,SAAS,CAACuC,QAAQ,GAAG,UAAUN,EAAE,EAAEC,OAAO,EAAE;IAChD,IAAItC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIuC,GAAG,GAAGvC,KAAK,CAACwC,MAAM;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5B,IAAIzC,KAAK,CAACyC,CAAC,CAAC,CAAClC,SAAS,IAAI,CAAC,IAAIP,KAAK,CAACyC,CAAC,CAAC,CAACG,KAAK,CAACrC,SAAS,IAAI,CAAC,IAAIP,KAAK,CAACyC,CAAC,CAAC,CAACI,KAAK,CAACtC,SAAS,IAAI,CAAC,EAAE;QAC7F8B,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEtC,KAAK,CAACyC,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EACD;EACA;AACF;AACA;AACA;EACE7C,KAAK,CAACQ,SAAS,CAAC0C,oBAAoB,GAAG,UAAUT,EAAE,EAAEU,SAAS,EAAEC,SAAS,EAAEV,OAAO,EAAE;IAClF,IAAI,EAAES,SAAS,YAAYhC,SAAS,CAAC,EAAE;MACrCgC,SAAS,GAAG,IAAI,CAAC9C,SAAS,CAACP,eAAe,CAACqD,SAAS,CAAC,CAAC;IACxD;IACA,IAAI,CAACA,SAAS,EAAE;MACd;IACF;IACA,IAAIE,QAAQ,GAAGD,SAAS,KAAK,KAAK,GAAG,UAAU,GAAGA,SAAS,KAAK,IAAI,GAAG,SAAS,GAAG,OAAO;IAC1F,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC1C,KAAK,CAACyC,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI,CAAC1C,KAAK,CAAC0C,CAAC,CAAC,CAACS,SAAS,GAAG,KAAK;IACjC;IACA,IAAIb,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAES,SAAS,EAAE,IAAI,CAAC,EAAE;MACrC;IACF;IACA,IAAII,KAAK,GAAG,CAACJ,SAAS,CAAC;IACvB,OAAOI,KAAK,CAACX,MAAM,EAAE;MACnB,IAAIY,WAAW,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC;MAC/B,IAAIrD,KAAK,GAAGoD,WAAW,CAACH,QAAQ,CAAC;MACjC,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrC,IAAIa,CAAC,GAAGtD,KAAK,CAACyC,CAAC,CAAC;QAChB,IAAIc,SAAS,GAAGD,CAAC,CAACV,KAAK,KAAKQ,WAAW,GAAGE,CAAC,CAACT,KAAK,GAAGS,CAAC,CAACV,KAAK;QAC3D,IAAI,CAACW,SAAS,CAACL,SAAS,EAAE;UACxB,IAAIb,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEiB,SAAS,EAAEH,WAAW,CAAC,EAAE;YAC5C;YACA;UACF;UACAD,KAAK,CAAClC,IAAI,CAACsC,SAAS,CAAC;UACrBA,SAAS,CAACL,SAAS,GAAG,IAAI;QAC5B;MACF;IACF;EACF,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAtD,KAAK,CAACQ,SAAS,CAACoD,MAAM,GAAG,YAAY;IACnC,IAAIpC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIc,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAInC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGxC,KAAK,CAACyC,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD1C,KAAK,CAAC0C,CAAC,CAAC,CAAClC,SAAS,GAAG,CAAC,CAAC;IACzB;IACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGnB,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAEhB,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD1C,KAAK,CAACqB,IAAI,CAACC,WAAW,CAACoB,CAAC,CAAC,CAAC,CAAClC,SAAS,GAAGkC,CAAC;IAC1C;IACAP,QAAQ,CAACwB,UAAU,CAAC,UAAUC,GAAG,EAAE;MACjC,IAAI9B,IAAI,GAAG7B,KAAK,CAACkC,QAAQ,CAACb,WAAW,CAACsC,GAAG,CAAC,CAAC;MAC3C,OAAO9B,IAAI,CAACe,KAAK,CAACrC,SAAS,IAAI,CAAC,IAAIsB,IAAI,CAACgB,KAAK,CAACtC,SAAS,IAAI,CAAC;IAC/D,CAAC,CAAC;IACF;IACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGvC,KAAK,CAACwC,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChDzC,KAAK,CAACyC,CAAC,CAAC,CAAClC,SAAS,GAAG,CAAC,CAAC;IACzB;IACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGL,QAAQ,CAACuB,KAAK,CAAC,CAAC,EAAEhB,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACpDzC,KAAK,CAACkC,QAAQ,CAACb,WAAW,CAACoB,CAAC,CAAC,CAAC,CAAClC,SAAS,GAAGkC,CAAC;IAC9C;EACF,CAAC;EACD;EACA;AACF;AACA;EACE7C,KAAK,CAACQ,SAAS,CAACwD,KAAK,GAAG,YAAY;IAClC,IAAIC,KAAK,GAAG,IAAIjE,KAAK,CAAC,IAAI,CAACO,SAAS,CAAC;IACrC,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,KAAK,CAACyC,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrCoB,KAAK,CAACvD,OAAO,CAACP,KAAK,CAAC0C,CAAC,CAAC,CAAC9C,EAAE,EAAEI,KAAK,CAAC0C,CAAC,CAAC,CAAClC,SAAS,CAAC;IAChD;IACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrC,IAAIa,CAAC,GAAGtD,KAAK,CAACyC,CAAC,CAAC;MAChBoB,KAAK,CAACtC,OAAO,CAAC+B,CAAC,CAACV,KAAK,CAACjD,EAAE,EAAE2D,CAAC,CAACT,KAAK,CAAClD,EAAE,EAAE2D,CAAC,CAAC/C,SAAS,CAAC;IACpD;IACA,OAAOsD,KAAK;EACd,CAAC;EACD;EACA,OAAOjE,KAAK;AACd,CAAC,CAAC,CAAC;AACH,IAAImB,SAAS,GAAG,aAAa,YAAY;EACvC,SAASA,SAASA,CAACpB,EAAE,EAAEY,SAAS,EAAE;IAChC,IAAI,CAACyB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC/B,KAAK,GAAG,EAAE;IACf,IAAI,CAACO,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACZ,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,EAAE,GAAGA,EAAE;IAC9B,IAAI,CAACY,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS;EACrD;EACA;AACF;AACA;EACEQ,SAAS,CAACX,SAAS,CAAC0D,MAAM,GAAG,YAAY;IACvC,OAAO,IAAI,CAAC9D,KAAK,CAACwC,MAAM;EAC1B,CAAC;EACD;AACF;AACA;EACEzB,SAAS,CAACX,SAAS,CAAC2D,QAAQ,GAAG,YAAY;IACzC,OAAO,IAAI,CAAC/B,OAAO,CAACQ,MAAM;EAC5B,CAAC;EACD;AACF;AACA;EACEzB,SAAS,CAACX,SAAS,CAAC4D,SAAS,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACjC,QAAQ,CAACS,MAAM;EAC7B,CAAC;EACDzB,SAAS,CAACX,SAAS,CAAC6D,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAI,IAAI,CAAC3D,SAAS,GAAG,CAAC,EAAE;MACtB;IACF;IACA,IAAIsD,KAAK,GAAG,IAAI,CAAC7C,SAAS;IAC1B,IAAImD,SAAS,GAAGN,KAAK,CAACzC,IAAI,CAACgD,YAAY,CAAC,IAAI,CAAC7D,SAAS,CAAC;IACvD,OAAO4D,SAAS,CAACF,QAAQ,CAACC,IAAI,CAAC;EACjC,CAAC;EACDnD,SAAS,CAACX,SAAS,CAACiE,sBAAsB,GAAG,YAAY;IACvD,IAAIC,WAAW,GAAG;MAChBzC,IAAI,EAAE,EAAE;MACRf,IAAI,EAAE;IACR,CAAC;IACD,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI8B,YAAY,GAAG,IAAI,CAACvE,KAAK,CAACyC,CAAC,CAAC;MAChC,IAAI8B,YAAY,CAAChE,SAAS,GAAG,CAAC,EAAE;QAC9B;MACF;MACA+D,WAAW,CAACzC,IAAI,CAACZ,IAAI,CAACsD,YAAY,CAAChE,SAAS,CAAC;MAC7C+D,WAAW,CAACxD,IAAI,CAACG,IAAI,CAACsD,YAAY,CAAC3B,KAAK,CAACrC,SAAS,EAAEgE,YAAY,CAAC1B,KAAK,CAACtC,SAAS,CAAC;IACnF;IACA,OAAO+D,WAAW;EACpB,CAAC;EACDvD,SAAS,CAACX,SAAS,CAACoE,wBAAwB,GAAG,YAAY;IACzD,IAAIC,iBAAiB,GAAGhF,MAAM,CAACiF,aAAa,CAAC,CAAC;IAC9C,IAAIC,iBAAiB,GAAGlF,MAAM,CAACiF,aAAa,CAAC,CAAC;IAC9C,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI8B,YAAY,GAAG,IAAI,CAACvE,KAAK,CAACyC,CAAC,CAAC;MAChC,IAAI8B,YAAY,CAAChE,SAAS,GAAG,CAAC,EAAE;QAC9B;MACF;MACAkE,iBAAiB,CAACG,GAAG,CAACL,YAAY,CAAChE,SAAS,EAAE,IAAI,CAAC;MACnD,IAAIsE,gBAAgB,GAAG,CAACN,YAAY,CAAC3B,KAAK,CAAC;MAC3C,IAAIkC,gBAAgB,GAAG,CAACP,YAAY,CAAC1B,KAAK,CAAC;MAC3C,IAAIkC,iBAAiB,GAAG,CAAC;MACzB,OAAOA,iBAAiB,GAAGF,gBAAgB,CAACrC,MAAM,EAAE;QAClD,IAAIwC,UAAU,GAAGH,gBAAgB,CAACE,iBAAiB,CAAC;QACpDA,iBAAiB,EAAE;QACnBJ,iBAAiB,CAACC,GAAG,CAACI,UAAU,CAACzE,SAAS,EAAE,IAAI,CAAC;QACjD,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAAChD,OAAO,CAACQ,MAAM,EAAEyC,CAAC,EAAE,EAAE;UAClDR,iBAAiB,CAACG,GAAG,CAACI,UAAU,CAAChD,OAAO,CAACiD,CAAC,CAAC,CAAC1E,SAAS,EAAE,IAAI,CAAC;UAC5DsE,gBAAgB,CAAC5D,IAAI,CAAC+D,UAAU,CAAChD,OAAO,CAACiD,CAAC,CAAC,CAACrC,KAAK,CAAC;QACpD;MACF;MACAmC,iBAAiB,GAAG,CAAC;MACrB,OAAOA,iBAAiB,GAAGD,gBAAgB,CAACtC,MAAM,EAAE;QAClD,IAAI0C,UAAU,GAAGJ,gBAAgB,CAACC,iBAAiB,CAAC;QACpDA,iBAAiB,EAAE;QACnBJ,iBAAiB,CAACC,GAAG,CAACM,UAAU,CAAC3E,SAAS,EAAE,IAAI,CAAC;QACjD,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,UAAU,CAACnD,QAAQ,CAACS,MAAM,EAAEyC,CAAC,EAAE,EAAE;UACnDR,iBAAiB,CAACG,GAAG,CAACM,UAAU,CAACnD,QAAQ,CAACkD,CAAC,CAAC,CAAC1E,SAAS,EAAE,IAAI,CAAC;UAC7DuE,gBAAgB,CAAC7D,IAAI,CAACiE,UAAU,CAACnD,QAAQ,CAACkD,CAAC,CAAC,CAACpC,KAAK,CAAC;QACrD;MACF;IACF;IACA,OAAO;MACLhB,IAAI,EAAE4C,iBAAiB,CAACU,IAAI,CAAC,CAAC;MAC9BrE,IAAI,EAAE6D,iBAAiB,CAACQ,IAAI,CAAC;IAC/B,CAAC;EACH,CAAC;EACD,OAAOpE,SAAS;AAClB,CAAC,CAAC,CAAC;AACH,IAAIe,SAAS,GAAG,aAAa,YAAY;EACvC,SAASA,SAASA,CAACN,EAAE,EAAEC,EAAE,EAAElB,SAAS,EAAE;IACpC,IAAI,CAACA,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,GAAGpB,EAAE;IACf,IAAI,CAACqB,KAAK,GAAGpB,EAAE;IACf,IAAI,CAAClB,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS;EACrD;EACA;EACAuB,SAAS,CAAC1B,SAAS,CAAC6D,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAI,IAAI,CAAC3D,SAAS,GAAG,CAAC,EAAE;MACtB;IACF;IACA,IAAIsD,KAAK,GAAG,IAAI,CAAC7C,SAAS;IAC1B,IAAImD,SAAS,GAAGN,KAAK,CAAC3B,QAAQ,CAACkC,YAAY,CAAC,IAAI,CAAC7D,SAAS,CAAC;IAC3D,OAAO4D,SAAS,CAACF,QAAQ,CAACC,IAAI,CAAC;EACjC,CAAC;EACDpC,SAAS,CAAC1B,SAAS,CAACiE,sBAAsB,GAAG,YAAY;IACvD,OAAO;MACLxC,IAAI,EAAE,CAAC,IAAI,CAACtB,SAAS,CAAC;MACtBO,IAAI,EAAE,CAAC,IAAI,CAAC8B,KAAK,CAACrC,SAAS,EAAE,IAAI,CAACsC,KAAK,CAACtC,SAAS;IACnD,CAAC;EACH,CAAC;EACDuB,SAAS,CAAC1B,SAAS,CAACoE,wBAAwB,GAAG,YAAY;IACzD,IAAIC,iBAAiB,GAAGhF,MAAM,CAACiF,aAAa,CAAC,CAAC;IAC9C,IAAIC,iBAAiB,GAAGlF,MAAM,CAACiF,aAAa,CAAC,CAAC;IAC9CD,iBAAiB,CAACG,GAAG,CAAC,IAAI,CAACrE,SAAS,EAAE,IAAI,CAAC;IAC3C,IAAI6E,WAAW,GAAG,CAAC,IAAI,CAACxC,KAAK,CAAC;IAC9B,IAAIyC,WAAW,GAAG,CAAC,IAAI,CAACxC,KAAK,CAAC;IAC9B,IAAIkC,iBAAiB,GAAG,CAAC;IACzB,OAAOA,iBAAiB,GAAGK,WAAW,CAAC5C,MAAM,EAAE;MAC7C,IAAIwC,UAAU,GAAGI,WAAW,CAACL,iBAAiB,CAAC;MAC/CA,iBAAiB,EAAE;MACnBJ,iBAAiB,CAACC,GAAG,CAACI,UAAU,CAACzE,SAAS,EAAE,IAAI,CAAC;MACjD,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAAChD,OAAO,CAACQ,MAAM,EAAEyC,CAAC,EAAE,EAAE;QAClDR,iBAAiB,CAACG,GAAG,CAACI,UAAU,CAAChD,OAAO,CAACiD,CAAC,CAAC,CAAC1E,SAAS,EAAE,IAAI,CAAC;QAC5D6E,WAAW,CAACnE,IAAI,CAAC+D,UAAU,CAAChD,OAAO,CAACiD,CAAC,CAAC,CAACrC,KAAK,CAAC;MAC/C;IACF;IACAmC,iBAAiB,GAAG,CAAC;IACrB,OAAOA,iBAAiB,GAAGM,WAAW,CAAC7C,MAAM,EAAE;MAC7C,IAAI0C,UAAU,GAAGG,WAAW,CAACN,iBAAiB,CAAC;MAC/CA,iBAAiB,EAAE;MACnBJ,iBAAiB,CAACC,GAAG,CAACM,UAAU,CAAC3E,SAAS,EAAE,IAAI,CAAC;MACjD,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,UAAU,CAACnD,QAAQ,CAACS,MAAM,EAAEyC,CAAC,EAAE,EAAE;QACnDR,iBAAiB,CAACG,GAAG,CAACM,UAAU,CAACnD,QAAQ,CAACkD,CAAC,CAAC,CAAC1E,SAAS,EAAE,IAAI,CAAC;QAC7D8E,WAAW,CAACpE,IAAI,CAACiE,UAAU,CAACnD,QAAQ,CAACkD,CAAC,CAAC,CAACpC,KAAK,CAAC;MAChD;IACF;IACA,OAAO;MACLhB,IAAI,EAAE4C,iBAAiB,CAACU,IAAI,CAAC,CAAC;MAC9BrE,IAAI,EAAE6D,iBAAiB,CAACQ,IAAI,CAAC;IAC/B,CAAC;EACH,CAAC;EACD,OAAOrD,SAAS;AAClB,CAAC,CAAC,CAAC;AACH,SAASwD,yBAAyBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACrD,OAAO;IACL;AACJ;AACA;IACIC,QAAQ,EAAE,SAAAA,CAAUC,SAAS,EAAE;MAC7B,IAAItE,IAAI,GAAG,IAAI,CAACmE,QAAQ,CAAC,CAACC,QAAQ,CAAC;MACnC,OAAOpE,IAAI,CAACuE,QAAQ,CAAC,CAAC,CAACC,GAAG,CAACxE,IAAI,CAACyE,iBAAiB,CAACH,SAAS,IAAI,OAAO,CAAC,EAAE,IAAI,CAACnF,SAAS,CAAC;IAC1F,CAAC;IACD;IACAuF,SAAS,EAAE,SAAAA,CAAUlE,GAAG,EAAEmE,KAAK,EAAE;MAC/B,IAAI,CAACxF,SAAS,IAAI,CAAC,IAAI,IAAI,CAACgF,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACQ,aAAa,CAAC,IAAI,CAACzF,SAAS,EAAEqB,GAAG,EAAEmE,KAAK,CAAC;IAC3F,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAUrE,GAAG,EAAE;MACxB,OAAO,IAAI,CAAC2D,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACU,aAAa,CAAC,IAAI,CAAC3F,SAAS,EAAEqB,GAAG,CAAC;IACpE,CAAC;IACDuE,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAEC,KAAK,EAAE;MAClC,IAAI,CAAC9F,SAAS,IAAI,CAAC,IAAI,IAAI,CAACgF,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACc,aAAa,CAAC,IAAI,CAAC/F,SAAS,EAAE6F,MAAM,EAAEC,KAAK,CAAC;IAC9F,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAAChB,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACgB,aAAa,CAAC,IAAI,CAACjG,SAAS,CAAC;IAC/D,CAAC;IACDkG,YAAY,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAAClB,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACkB,gBAAgB,CAAC,IAAI,CAACnG,SAAS,CAAC;IAClE,CAAC;IACDc,WAAW,EAAE,SAAAA,CAAA,EAAY;MACvB,OAAO,IAAI,CAACkE,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACnE,WAAW,CAAC,IAAI,CAACd,SAAS,CAAC;IAC7D;EACF,CAAC;AACH;AACA;AACA;AACA;AACAd,MAAM,CAACkH,KAAK,CAAC5F,SAAS,EAAEuE,yBAAyB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACvE7F,MAAM,CAACkH,KAAK,CAAC7E,SAAS,EAAEwD,yBAAyB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC3E,eAAe1F,KAAK;AACpB,SAASmB,SAAS,EAAEe,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}